#!/usr/bin/env python3
"""
Test script to create a client notification for testing invitation responses
"""

import mysql.connector
from datetime import datetime

def create_test_notification():
    try:
        # Database connection
        conn = mysql.connector.connect(
            host='**************',
            user='giggenius',
            password='Happiness1524!',
            database='giggenius'
        )
        cursor = conn.cursor(dictionary=True)

        # Create client_notifications table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS client_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                genius_id INT NOT NULL,
                job_id INT NOT NULL,
                notification_type ENUM('invitation_accepted', 'invitation_declined') NOT NULL,
                job_title VARCHAR(255),
                genius_name VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_read BOOLEAN DEFAULT FALSE,
                INDEX idx_client_id (client_id),
                INDEX idx_created_at (created_at)
            )
        """)

        # Get <PERSON>'s client ID from the database
        cursor.execute("SELECT id FROM approve_client WHERE email = '<EMAIL>' LIMIT 1")
        client_result = cursor.fetchone()
        if not client_result:
            print("❌ Could not find Carlo's client account")
            return
        client_id = client_result['id']
        print(f"📧 Found Carlo's client ID: {client_id}")
        
        # Insert a test notification for invitation accepted
        cursor.execute("""
            INSERT INTO client_notifications 
            (client_id, genius_id, job_id, notification_type, job_title, genius_name, is_read)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (client_id, 2, 1, 'invitation_accepted', 'Test Web Development Job', 'John Doe', False))

        # Insert another test notification for invitation declined
        cursor.execute("""
            INSERT INTO client_notifications 
            (client_id, genius_id, job_id, notification_type, job_title, genius_name, is_read)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (client_id, 3, 2, 'invitation_declined', 'Test Mobile App Development', 'Jane Smith', False))

        conn.commit()
        print("✅ Test notifications created successfully!")

        # Verify the notifications were created
        cursor.execute("""
            SELECT * FROM client_notifications 
            WHERE client_id = %s 
            ORDER BY created_at DESC
        """, (client_id,))
        
        notifications = cursor.fetchall()
        print(f"\n📧 Found {len(notifications)} notifications for client {client_id}:")
        for notif in notifications:
            print(f"   - {notif['notification_type']}: {notif['genius_name']} - {notif['job_title']}")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    create_test_notification()
