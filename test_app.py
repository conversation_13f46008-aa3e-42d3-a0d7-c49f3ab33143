#!/usr/bin/env python3
"""
Test Flask Application for Genius Recommendations Feature
Uses the same database as the main app but runs independently for testing
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import mysql.connector
from mysql.connector import pooling
import json
import os
from datetime import datetime
import traceback

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'test_secret_key_for_recommendations'

# Database configuration (same as main app)
DB_CONFIG = {
    'host': '**************',
    'user': 'giggenius_user',  # Fixed: using same username as main app
    'password': 'Happiness1524!',
    'database': 'giggenius',
    'connect_timeout': 10,
    'use_pure': True,
    'autocommit': True,
    'buffered': True,
    'consume_results': True
}

# Create connection pool
try:
    connection_pool = pooling.MySQLConnectionPool(
        pool_name="test_pool",
        pool_size=5,
        pool_reset_session=True,
        **DB_CONFIG
    )
    print("✅ Database connection pool created successfully")
except Exception as e:
    print(f"❌ Error creating connection pool: {e}")
    connection_pool = None

def get_db_connection():
    """Get database connection with retry logic (same as main app)"""
    import time
    max_retries = 3
    retry_delay = 1
    last_error = None

    for attempt in range(max_retries):
        try:
            if connection_pool:
                connection = connection_pool.get_connection()
            else:
                connection = mysql.connector.connect(**DB_CONFIG)

            if connection is not None:
                # Test the connection
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return connection
        except Exception as e:
            last_error = e
            print(f"Connection attempt {attempt+1}/{max_retries} failed: {e}")

            # Close faulty connection
            if 'connection' in locals() and connection is not None:
                try:
                    connection.close()
                except:
                    pass

        # Wait before retrying
        if attempt < max_retries - 1:
            sleep_time = retry_delay * (2 ** attempt)
            print(f"Retrying in {sleep_time} seconds...")
            time.sleep(sleep_time)

    # Last resort - direct connection
    print("All pooled connections failed. Trying direct connection...")
    try:
        direct_conn = mysql.connector.connect(**DB_CONFIG)
        cursor = direct_conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        return direct_conn
    except Exception as e:
        print(f"Direct connection failed: {e}")
        if last_error:
            print(f"Original error: {last_error}")
        raise Exception(f"Database connection failed: {e}")

# Simple login decorator for testing
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # For testing, auto-login as a client
            session['user_id'] = 1
            session['user_type'] = 'client'
            session['first_name'] = 'Test'
            session['last_name'] = 'Client'
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/')
def index():
    """Test app homepage"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Genius Recommendations Test App</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                max-width: 800px; 
                margin: 50px auto; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            .container {
                background: white;
                color: #333;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            h1 { color: #1976d2; text-align: center; margin-bottom: 30px; }
            .feature-list { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
            .feature-list li { margin: 10px 0; }
            .btn { 
                display: inline-block;
                padding: 15px 30px; 
                background: #1976d2; 
                color: white; 
                text-decoration: none; 
                border-radius: 8px; 
                margin: 10px; 
                transition: all 0.3s ease;
                font-weight: 600;
            }
            .btn:hover { 
                background: #1565c0; 
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(25, 118, 210, 0.3);
            }
            .btn-secondary { background: #e91e63; }
            .btn-secondary:hover { background: #c2185b; }
            .btn-success { background: #4caf50; }
            .btn-success:hover { background: #45a049; }
            .status { 
                padding: 10px; 
                border-radius: 5px; 
                margin: 10px 0; 
                background: #e8f5e8; 
                border-left: 4px solid #4caf50; 
            }
            .links { text-align: center; margin-top: 30px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🌟 Genius Recommendations Test App</h1>
            
            <div class="status">
                <strong>✅ Status:</strong> Connected to production database (giggenius)<br>
                <strong>🔐 Auto-Login:</strong> Test Client (ID: 1)<br>
                <strong>🎯 Purpose:</strong> Test genius recommendations feature safely
            </div>
            
            <div class="feature-list">
                <h3>🚀 Available Test Features:</h3>
                <ul>
                    <li><strong>Sample Modal:</strong> Interactive demo with sample data</li>
                    <li><strong>Live API:</strong> Real genius recommendations from database</li>
                    <li><strong>Database Check:</strong> Verify genius data availability</li>
                    <li><strong>Job Simulation:</strong> Test different job types and matching</li>
                    <li><strong>Filter Testing:</strong> Experience level and position filtering</li>
                </ul>
            </div>
            
            <div class="links">
                <a href="/job_posting_sample" class="btn">� Job Posting Sample</a>
                <a href="/simple_expertise_match" class="btn btn-success">🎯 Simple Expertise Match</a>
                <a href="/sample_recommendations" class="btn">📱 Sample Modal Demo</a>
                <a href="/check_database" class="btn">🗄️ Database Check</a>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h4>⚠️ Important Notes:</h4>
                <ul>
                    <li>This test app uses the same database as your main application</li>
                    <li>All data is real - no test data will be created</li>
                    <li>Auto-logged in as Test Client for convenience</li>
                    <li>Safe to test - no data modifications</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/sample_recommendations')
def sample_recommendations():
    """Sample page with demo data"""
    return render_template('sample_recommendations.html')

@app.route('/job_posting_sample')
@login_required
def job_posting_sample():
    """Simple job posting form that shows recommendations after posting"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Job Posting Sample</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                margin: 0;
                padding: 20px;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #1976d2, #e91e63);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 2.2em;
            }
            .form-container {
                padding: 40px;
            }
            .form-group {
                margin-bottom: 25px;
            }
            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #333;
                font-size: 14px;
            }
            .form-group input,
            .form-group select,
            .form-group textarea {
                width: 100%;
                padding: 12px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                font-size: 14px;
                transition: border-color 0.3s ease;
                box-sizing: border-box;
            }
            .form-group input:focus,
            .form-group select:focus,
            .form-group textarea:focus {
                outline: none;
                border-color: #1976d2;
                box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            }
            .form-group textarea {
                resize: vertical;
                min-height: 100px;
            }
            .required {
                color: #e91e63;
            }
            .btn {
                background: linear-gradient(135deg, #1976d2, #1565c0);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 100%;
                margin-top: 20px;
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
            }
            .btn:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .back-btn {
                background: #6c757d;
                color: white;
                text-decoration: none;
                display: inline-block;
                padding: 10px 20px;
                border-radius: 5px;
                margin-bottom: 20px;
                transition: background 0.3s ease;
            }
            .back-btn:hover {
                background: #5a6268;
            }
            .loading {
                text-align: center;
                padding: 20px;
                color: #666;
                display: none;
            }
            .spinner {
                border: 4px solid #f3f3f3;
                border-top: 4px solid #1976d2;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .success-message {
                background: #d4edda;
                color: #155724;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #28a745;
                margin: 20px 0;
                display: none;
            }
            .error-message {
                background: #f8d7da;
                color: #721c24;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #dc3545;
                margin: 20px 0;
                display: none;
            }
            .form-row {
                display: flex;
                gap: 20px;
            }
            .form-row .form-group {
                flex: 1;
            }
            .expertise-highlight {
                background: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
            }
            .expertise-highlight h3 {
                margin: 0 0 10px 0;
                color: #856404;
            }

            /* Modal Styles */
            .modal {
                display: none;
                position: fixed;
                z-index: 9999;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                backdrop-filter: blur(5px);
            }

            .modal-content {
                background-color: white;
                margin: 2% auto;
                padding: 0;
                border-radius: 15px;
                width: 90%;
                max-width: 1000px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 25px 50px rgba(0,0,0,0.2);
                animation: modalSlideIn 0.3s ease-out;
            }

            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(-50px) scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .modal-header {
                background: linear-gradient(135deg, #1976d2, #e91e63);
                color: white;
                padding: 25px 30px;
                border-radius: 15px 15px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .modal-header h2 {
                margin: 0;
                font-size: 1.8em;
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .close {
                color: white;
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }

            .close:hover {
                background: rgba(255,255,255,0.2);
                transform: rotate(90deg);
            }

            .modal-body {
                padding: 30px;
            }

            .job-summary {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 25px;
                border-left: 4px solid #1976d2;
            }

            .genius-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }

            .genius-card {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .genius-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                border-color: #1976d2;
            }

            .genius-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #1976d2, #e91e63);
            }

            .genius-name {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }

            .genius-details {
                margin: 8px 0;
                color: #666;
                font-size: 14px;
            }

            .match-score {
                position: absolute;
                top: 15px;
                right: 15px;
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
            }

            .expertise-badge {
                background: #e3f2fd;
                color: #1976d2;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
                display: inline-block;
                margin-left: 10px;
            }

            .match-reasons {
                background: #e8f5e8;
                padding: 10px;
                border-radius: 6px;
                margin: 10px 0;
                font-size: 12px;
                border-left: 3px solid #4caf50;
            }

            .match-reasons strong {
                color: #2e7d32;
            }

            .no-results {
                text-align: center;
                padding: 40px;
                color: #666;
            }

            .no-results i {
                font-size: 48px;
                color: #ccc;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 Create a Job Post</h1>
                <p>Fill out the form below and see genius recommendations based on your expertise requirement</p>
            </div>

            <div class="form-container">
                <a href="/" class="back-btn">🏠 Back to Home</a>

                <form id="jobForm">
                    <div class="form-group">
                        <label for="jobTitle">Job Title <span class="required">*</span></label>
                        <input type="text" id="jobTitle" name="jobTitle" placeholder="e.g., Frontend Developer, UI/UX Designer" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="category">Category <span class="required">*</span></label>
                            <select id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Web Development">Web Development</option>
                                <option value="Mobile Development">Mobile Development</option>
                                <option value="Design">Design</option>
                                <option value="Digital Marketing">Digital Marketing</option>
                                <option value="Data Science">Data Science</option>
                                <option value="Writing & Content">Writing & Content</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="budget">Budget (USD) <span class="required">*</span></label>
                            <input type="number" id="budget" name="budget" placeholder="e.g., 1000" min="1" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Job Description <span class="required">*</span></label>
                        <textarea id="description" name="description" placeholder="Describe what you need done..." required></textarea>
                    </div>

                    <div class="expertise-highlight">
                        <h3>🎯 Job Requirements</h3>
                        <p>Select category and expertise level. System will automatically find matching geniuses!</p>

                        <div class="form-group">
                            <label for="expertise">Required Expertise Level <span class="required">*</span></label>
                            <select id="expertise" name="expertise" required>
                                <option value="">Select Expertise Level</option>
                                <option value="entry">Entry Level - New to the field</option>
                                <option value="intermediate">Intermediate - Some experience</option>
                                <option value="expert">Expert - Highly experienced</option>
                            </select>
                        </div>

                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">🤖 Smart Matching</h4>
                            <p style="margin: 0; font-size: 14px; color: #2e7d32;">
                                The system will automatically match geniuses based on:
                                <br>• <strong>Category</strong> → Genius position and skills
                                <br>• <strong>Expertise Level</strong> → Exact expertise match
                                <br>• <strong>Relevance</strong> → Introduction and professional summary
                            </p>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="duration">Project Duration</label>
                            <select id="duration" name="duration">
                                <option value="">Select Duration</option>
                                <option value="1-3 days">1-3 days</option>
                                <option value="1 week">1 week</option>
                                <option value="2-4 weeks">2-4 weeks</option>
                                <option value="1-3 months">1-3 months</option>
                                <option value="3+ months">3+ months</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="urgency">Urgency</label>
                            <select id="urgency" name="urgency">
                                <option value="">Select Urgency</option>
                                <option value="low">Low - No rush</option>
                                <option value="medium">Medium - Standard timeline</option>
                                <option value="high">High - ASAP</option>
                            </select>
                        </div>
                    </div>

                    <button type="submit" class="btn" id="submitBtn">
                        🚀 Post Job & See Recommendations
                    </button>
                </form>

                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>Posting your job and finding matching geniuses...</p>
                </div>

                <div id="successMessage" class="success-message"></div>
                <div id="errorMessage" class="error-message"></div>
            </div>
        </div>

        <!-- Genius Recommendations Modal -->
        <div id="recommendationsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>
                        <i class="fas fa-star" style="color: #ffd700;"></i>
                        Recommended Geniuses
                    </h2>
                    <span class="close" onclick="closeModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="jobSummary" class="job-summary">
                        <!-- Job summary will be populated here -->
                    </div>

                    <div id="recommendationsContent">
                        <!-- Recommendations will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.getElementById('jobForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(this);
                const jobData = {
                    title: formData.get('jobTitle'),
                    category: formData.get('category'),
                    description: formData.get('description'),
                    expertise: formData.get('expertise'),
                    budget: formData.get('budget'),
                    duration: formData.get('duration'),
                    urgency: formData.get('urgency')
                };

                // Validate required fields
                if (!jobData.title || !jobData.category || !jobData.description || !jobData.expertise || !jobData.budget) {
                    showError('Please fill in all required fields');
                    return;
                }

                // Show loading
                document.getElementById('loading').style.display = 'block';
                document.getElementById('submitBtn').disabled = true;
                document.getElementById('submitBtn').textContent = 'Posting Job...';

                try {
                    // Simulate job posting (you can add actual job posting logic here)
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // Show success message
                    showSuccess(`Job "${jobData.title}" posted successfully! Finding geniuses with "${jobData.expertise}" expertise level...`);

                    // Wait a moment then show recommendations
                    setTimeout(() => {
                        showRecommendations(jobData);
                    }, 1000);

                } catch (error) {
                    showError('Error posting job: ' + error.message);
                } finally {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('submitBtn').disabled = false;
                    document.getElementById('submitBtn').textContent = '🚀 Post Job & See Recommendations';
                }
            });

            async function showRecommendations(jobData) {
                try {
                    // Fetch recommendations based on category and expertise (smart matching)
                    const response = await fetch('/api/smart_recommendations', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            expertise: jobData.expertise,
                            category: jobData.category,
                            title: jobData.title,
                            description: jobData.description
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Show recommendations in modal
                        displayRecommendationsModal(jobData, data.recommendations, data.is_fallback, data.message);
                    } else {
                        showError('Error fetching recommendations: ' + data.error);
                    }
                } catch (error) {
                    showError('Error fetching recommendations: ' + error.message);
                }
            }

            function displayRecommendationsModal(jobData, recommendations, isFallback = false, fallbackMessage = '') {
                // Update job summary
                const jobSummary = document.getElementById('jobSummary');

                const statusColor = isFallback ? '#fff3cd' : '#e8f5e8';
                const statusBorder = isFallback ? '#ffc107' : '#28a745';
                const statusIcon = isFallback ? '⚠️' : '🤖';
                const statusText = isFallback ? fallbackMessage : 'Showing geniuses that match your category and expertise level';

                jobSummary.innerHTML = `
                    <h3>📋 Your Job: "${jobData.title}"</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div><strong>Category:</strong> ${jobData.category}</div>
                        <div><strong>Expertise:</strong> ${jobData.expertise}</div>
                        <div><strong>Budget:</strong> $${jobData.budget}</div>
                        <div><strong>Found:</strong> ${recommendations.length} ${isFallback ? 'alternative' : ''} matches</div>
                    </div>
                    <div style="margin-top: 15px; padding: 15px; background: ${statusColor}; border-radius: 8px; border-left: 4px solid ${statusBorder};">
                        <strong>${statusIcon} ${isFallback ? 'Alternative Results' : 'Smart Matching'}:</strong> ${statusText}
                        ${isFallback ? `
                            <div style="margin-top: 10px; font-size: 14px;">
                                <strong>💡 Suggestions:</strong>
                                <br>• Try a different category that better matches your needs
                                <br>• Consider a different expertise level
                                <br>• Post your job anyway - geniuses can still apply manually
                            </div>
                        ` : ''}
                    </div>
                `;

                // Update recommendations content
                const content = document.getElementById('recommendationsContent');

                if (recommendations.length === 0) {
                    content.innerHTML = `
                        <div class="no-results">
                            <i class="fas fa-search" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                            <h3>No matching geniuses found</h3>
                            <p>No geniuses match your <strong>${jobData.category}</strong> category with <strong>${jobData.expertise}</strong> expertise level.</p>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left;">
                                <h4 style="margin-top: 0; color: #1976d2;">💡 What you can do:</h4>
                                <div style="margin: 10px 0;">
                                    <strong>1. Try Different Categories:</strong>
                                    <br>• Web Development, Mobile Development, Design
                                    <br>• Digital Marketing, Data Science, Writing & Content
                                </div>
                                <div style="margin: 10px 0;">
                                    <strong>2. Adjust Expertise Level:</strong>
                                    <br>• Try "Intermediate" if you selected "Expert"
                                    <br>• Try "Expert" if you selected "Entry"
                                </div>
                                <div style="margin: 10px 0;">
                                    <strong>3. Post Anyway:</strong>
                                    <br>• Your job will still be visible to all geniuses
                                    <br>• Geniuses can apply even if not auto-recommended
                                </div>
                            </div>

                            <div style="margin-top: 20px;">
                                <button onclick="closeModal()" style="background: #1976d2; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px;">
                                    Close & Try Again
                                </button>
                                <button onclick="postJobAnyway()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px;">
                                    Post Job Anyway
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    content.innerHTML = `
                        <div class="genius-grid">
                            ${recommendations.map(genius => `
                                <div class="genius-card">
                                    <div class="match-score">${genius.match_score}% Match</div>
                                    <div class="genius-name">${genius.first_name} ${genius.last_name}</div>
                                    <div class="genius-details">
                                        <strong>Position:</strong> ${genius.position || 'Not specified'}
                                        <span class="expertise-badge">${genius.expertise || 'N/A'}</span>
                                    </div>
                                    <div class="genius-details"><strong>Country:</strong> ${genius.country || 'Not specified'}</div>
                                    <div class="genius-details"><strong>Rate:</strong> $${genius.hourly_rate || 'Not specified'}/hour</div>
                                    ${genius.match_reasons && genius.match_reasons.length > 0 ? `
                                        <div class="match-reasons">
                                            <strong>Why this genius matches:</strong><br>
                                            ${genius.match_reasons.map(reason => `• ${reason}`).join('<br>')}
                                        </div>
                                    ` : ''}
                                    ${genius.introduction ? `
                                        <div class="genius-details" style="margin-top: 10px;">
                                            <strong>About:</strong> ${genius.introduction.substring(0, 120)}${genius.introduction.length > 120 ? '...' : ''}
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }

                // Show modal
                document.getElementById('recommendationsModal').style.display = 'block';
            }

            function closeModal() {
                document.getElementById('recommendationsModal').style.display = 'none';
            }

            // Close modal when clicking outside
            window.onclick = function(event) {
                const modal = document.getElementById('recommendationsModal');
                if (event.target === modal) {
                    closeModal();
                }
            }

            // Close modal with Escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeModal();
                }
            });

            function postJobAnyway() {
                closeModal();
                showSuccess('✅ Job posted successfully! Your job is now visible to all geniuses, even though no automatic recommendations were found. Geniuses can still discover and apply to your job manually.');

                // Reset form
                document.getElementById('jobForm').reset();
            }

            function showSuccess(message) {
                const successDiv = document.getElementById('successMessage');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
                document.getElementById('errorMessage').style.display = 'none';
            }

            function showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            }
        </script>
    </body>
    </html>
    """

@app.route('/check_database')
@login_required
def check_database():
    """Check database connection and genius data"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # Get genius count
        cursor.execute("SELECT COUNT(*) as total FROM approve_genius")
        total_count = cursor.fetchone()['total']
        
        # Get sample geniuses with all fields
        cursor.execute("""
            SELECT id, first_name, last_name, position, expertise, 
                   introduction, professional_sum, country, hourly_rate, availability
            FROM approve_genius 
            LIMIT 10
        """)
        sample_geniuses = cursor.fetchall()
        
        # Get unique positions
        cursor.execute("SELECT DISTINCT position FROM approve_genius WHERE position IS NOT NULL")
        positions = [row['position'] for row in cursor.fetchall()]
        
        # Get unique expertise levels
        cursor.execute("SELECT DISTINCT expertise FROM approve_genius WHERE expertise IS NOT NULL")
        expertise_levels = [row['expertise'] for row in cursor.fetchall()]
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Database Check Results</title>
            <style>
                body {{ font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }}
                .success {{ background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin: 10px 0; }}
                .info {{ background: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #17a2b8; margin: 10px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .btn {{ display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }}
                .btn:hover {{ background: #0056b3; }}
                pre {{ background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }}
            </style>
        </head>
        <body>
            <h1>🗄️ Database Check Results</h1>
            
            <div class="success">
                <h3>✅ Database Connection: Successful</h3>
                <p><strong>Total Geniuses:</strong> {total_count}</p>
                <p><strong>Sample Size:</strong> {len(sample_geniuses)} (showing first 10)</p>
            </div>
            
            <div class="info">
                <h3>📊 Available Data Summary</h3>
                <p><strong>Unique Positions:</strong> {len(positions)}</p>
                <p><strong>Unique Expertise Levels:</strong> {len(expertise_levels)}</p>
            </div>
            
            <h3>👥 Sample Geniuses</h3>
            <table>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Position</th>
                    <th>Expertise</th>
                    <th>Country</th>
                    <th>Rate</th>
                    <th>Availability</th>
                </tr>
                {''.join([f'''
                <tr>
                    <td>{genius['id']}</td>
                    <td>{genius['first_name']} {genius['last_name']}</td>
                    <td>{genius['position'] or 'N/A'}</td>
                    <td>{genius['expertise'] or 'N/A'}</td>
                    <td>{genius['country'] or 'N/A'}</td>
                    <td>${genius['hourly_rate'] or 'N/A'}</td>
                    <td>{genius['availability'] or 'N/A'}</td>
                </tr>
                ''' for genius in sample_geniuses])}
            </table>
            
            <h3>🎯 Available Positions</h3>
            <pre>{', '.join(positions[:20])}{'...' if len(positions) > 20 else ''}</pre>
            
            <h3>📈 Expertise Levels</h3>
            <pre>{', '.join(expertise_levels)}</pre>
            
            <div style="margin-top: 30px;">
                <a href="/" class="btn">🏠 Back to Home</a>
                <a href="/test_live_recommendations" class="btn">🔴 Test Live API</a>
            </div>
        </body>
        </html>
        """
        
    except Exception as e:
        return f"""
        <h1>❌ Database Check Failed</h1>
        <div style="background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">
            <strong>Error:</strong> {str(e)}
        </div>
        <a href="/" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px;">🏠 Back to Home</a>
        """
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@app.route('/simple_expertise_match')
@login_required
def simple_expertise_match():
    """Simple expertise level matching test"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Expertise Match Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 1000px;
                margin: 20px auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            h1 { color: #1976d2; text-align: center; margin-bottom: 30px; }
            .test-section {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #1976d2;
            }
            .btn {
                padding: 15px 25px;
                background: #1976d2;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                margin: 10px;
                font-size: 16px;
                transition: all 0.3s ease;
            }
            .btn:hover {
                background: #1565c0;
                transform: translateY(-2px);
            }
            .btn-entry { background: #4caf50; }
            .btn-entry:hover { background: #45a049; }
            .btn-intermediate { background: #ff9800; }
            .btn-intermediate:hover { background: #f57c00; }
            .btn-expert { background: #e91e63; }
            .btn-expert:hover { background: #c2185b; }
            .result {
                background: white;
                padding: 20px;
                border-radius: 8px;
                margin: 15px 0;
                border-left: 4px solid #28a745;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .error { border-left-color: #dc3545; }
            .genius-card {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                transition: all 0.3s ease;
            }
            .genius-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .genius-name {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 5px;
            }
            .genius-details {
                color: #666;
                margin: 5px 0;
            }
            .expertise-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: bold;
                margin-left: 10px;
            }
            .expertise-entry { background: #e8f5e8; color: #2e7d32; }
            .expertise-intermediate { background: #fff3e0; color: #f57c00; }
            .expertise-expert { background: #fce4ec; color: #c2185b; }
            .loading { text-align: center; padding: 20px; color: #666; }
            .back-btn {
                background: #6c757d;
                text-decoration: none;
                display: inline-block;
                margin-top: 20px;
            }
            .back-btn:hover { background: #5a6268; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 Simple Expertise Level Matching</h1>

            <div class="test-section">
                <h2>How it works:</h2>
                <p>Select an expertise level below. The system will fetch all geniuses from the <code>approve_genius</code> table that have the exact same expertise level.</p>
                <p><strong>Simple Logic:</strong> If you select "intermediate", it shows only geniuses with expertise = "intermediate"</p>
            </div>

            <div class="test-section">
                <h3>Select Expertise Level:</h3>
                <button class="btn btn-entry" onclick="fetchByExpertise('entry')">
                    🟢 Entry Level
                </button>
                <button class="btn btn-intermediate" onclick="fetchByExpertise('intermediate')">
                    🟡 Intermediate
                </button>
                <button class="btn btn-expert" onclick="fetchByExpertise('expert')">
                    🔴 Expert
                </button>
                <button class="btn" onclick="fetchByExpertise('all')">
                    📊 Show All Expertise Levels
                </button>
            </div>

            <div id="result"></div>

            <a href="/" class="btn back-btn">🏠 Back to Home</a>
        </div>

        <script>
            async function fetchByExpertise(expertiseLevel) {
                const resultDiv = document.getElementById('result');

                resultDiv.innerHTML = '<div class="loading">🔄 Fetching geniuses with expertise level: ' + expertiseLevel + '...</div>';

                try {
                    const response = await fetch('/api/fetch_by_expertise', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ expertise: expertiseLevel })
                    });

                    const data = await response.json();

                    if (data.success) {
                        if (data.geniuses.length === 0) {
                            resultDiv.innerHTML = `
                                <div class="result">
                                    <h3>📭 No Results Found</h3>
                                    <p>No geniuses found with expertise level: <strong>${expertiseLevel}</strong></p>
                                </div>
                            `;
                        } else {
                            resultDiv.innerHTML = `
                                <div class="result">
                                    <h3>✅ Found ${data.geniuses.length} genius(es) with expertise: "${expertiseLevel}"</h3>
                                    <p><strong>Query used:</strong> <code>${data.query_info}</code></p>

                                    <div class="geniuses-list">
                                        ${data.geniuses.map(genius => `
                                            <div class="genius-card">
                                                <div class="genius-name">
                                                    ${genius.first_name} ${genius.last_name}
                                                    <span class="expertise-badge expertise-${genius.expertise || 'entry'}">
                                                        ${genius.expertise || 'N/A'}
                                                    </span>
                                                </div>
                                                <div class="genius-details">
                                                    <strong>Position:</strong> ${genius.position || 'Not specified'}
                                                </div>
                                                <div class="genius-details">
                                                    <strong>Country:</strong> ${genius.country || 'Not specified'}
                                                </div>
                                                <div class="genius-details">
                                                    <strong>Hourly Rate:</strong> $${genius.hourly_rate || 'Not specified'}
                                                </div>
                                                ${genius.introduction ? `
                                                    <div class="genius-details">
                                                        <strong>Introduction:</strong> ${genius.introduction.substring(0, 150)}${genius.introduction.length > 150 ? '...' : ''}
                                                    </div>
                                                ` : ''}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            `;
                        }
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ Error</h3>
                                <p><strong>Error:</strong> ${data.error}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p><strong>Error:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

@app.route('/test_live_recommendations')
@login_required
def test_live_recommendations():
    """Test page for live recommendations API"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Live Recommendations Test</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; }
            .test-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .btn { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn:hover { background: #0056b3; }
            .btn-success { background: #28a745; }
            .btn-success:hover { background: #1e7e34; }
            .btn-warning { background: #ffc107; color: #212529; }
            .btn-warning:hover { background: #e0a800; }
            .result { background: white; padding: 20px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #28a745; }
            .error { border-left-color: #dc3545; }
            pre { background: #f8f9fa; padding: 15px; border-radius: 3px; overflow-x: auto; max-height: 400px; }
            .loading { text-align: center; padding: 20px; color: #666; }
        </style>
    </head>
    <body>
        <h1>🔴 Live Genius Recommendations API Test</h1>
        
        <div class="test-section">
            <h2>Test Different Job Types</h2>
            <p>Click the buttons below to test the live recommendations API with different job requirements:</p>
            
            <button class="btn" onclick="testRecommendations('web')">💻 Web Development Job</button>
            <button class="btn btn-success" onclick="testRecommendations('design')">🎨 Design Job</button>
            <button class="btn btn-warning" onclick="testRecommendations('marketing')">📢 Marketing Job</button>
            <button class="btn" onclick="testRecommendations('data')">📊 Data Science Job</button>
            <button class="btn" onclick="testRecommendations('mobile')">📱 Mobile Development</button>
        </div>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <a href="/" style="display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px;">🏠 Back to Home</a>
        </div>
        
        <script>
            const jobTypes = {
                web: {
                    category: 'Web Development',
                    skills: ['JavaScript', 'React', 'Node.js', 'HTML', 'CSS', 'Python'],
                    experience_level: 'intermediate',
                    specialty: 'Frontend Development'
                },
                design: {
                    category: 'Design',
                    skills: ['Photoshop', 'Illustrator', 'UI/UX', 'Figma', 'Adobe XD'],
                    experience_level: 'expert',
                    specialty: 'UI/UX Design'
                },
                marketing: {
                    category: 'Digital Marketing',
                    skills: ['SEO', 'Google Ads', 'Social Media', 'Analytics', 'Content Marketing'],
                    experience_level: 'intermediate',
                    specialty: 'SEO'
                },
                data: {
                    category: 'Data Science',
                    skills: ['Python', 'R', 'SQL', 'Machine Learning', 'Statistics'],
                    experience_level: 'expert',
                    specialty: 'Data Analysis'
                },
                mobile: {
                    category: 'Mobile Development',
                    skills: ['React Native', 'Flutter', 'iOS', 'Android', 'Swift'],
                    experience_level: 'intermediate',
                    specialty: 'Mobile Apps'
                }
            };
            
            async function testRecommendations(jobType) {
                const resultDiv = document.getElementById('result');
                const jobData = jobTypes[jobType];
                
                resultDiv.innerHTML = '<div class="loading">🔄 Fetching live recommendations from database...</div>';
                
                try {
                    const response = await fetch('/get_genius_recommendations', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(jobData)
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result">
                                <h3>✅ Success! Found ${data.total_count} recommendations for ${jobData.category}</h3>
                                <p><strong>Job Requirements:</strong></p>
                                <ul>
                                    <li><strong>Category:</strong> ${jobData.category}</li>
                                    <li><strong>Experience Level:</strong> ${jobData.experience_level}</li>
                                    <li><strong>Skills:</strong> ${jobData.skills.join(', ')}</li>
                                    <li><strong>Specialty:</strong> ${jobData.specialty}</li>
                                </ul>
                                
                                <h4>🎯 Top Recommendations:</h4>
                                ${data.recommendations.slice(0, 5).map(genius => `
                                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">
                                        <h5 style="margin: 0 0 10px 0; color: #1976d2;">
                                            ${genius.first_name} ${genius.last_name} 
                                            <span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">
                                                ${genius.match_score}% Match
                                            </span>
                                        </h5>
                                        <p><strong>Position:</strong> ${genius.position || 'N/A'}</p>
                                        <p><strong>Experience:</strong> ${genius.expertise_level || 'N/A'}</p>
                                        <p><strong>Country:</strong> ${genius.country || 'N/A'}</p>
                                        <p><strong>Rate:</strong> $${genius.hourly_rate || 'N/A'}/hour</p>
                                        <p><strong>Why this genius matches:</strong> ${genius.match_reasons.join(', ')}</p>
                                        ${genius.professional_summary ? `<p><strong>Summary:</strong> ${genius.professional_summary.substring(0, 150)}...</p>` : ''}
                                    </div>
                                `).join('')}
                                
                                <details style="margin-top: 20px;">
                                    <summary style="cursor: pointer; font-weight: bold; color: #1976d2;">📋 View Full API Response</summary>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="result error">
                                <h3>❌ API Error</h3>
                                <p><strong>Error:</strong> ${data.error}</p>
                                <p><strong>Job Type:</strong> ${jobData.category}</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Network Error</h3>
                            <p><strong>Error:</strong> ${error.message}</p>
                            <p><strong>Job Type:</strong> ${jobData.category}</p>
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    """

@app.route('/get_genius_recommendations', methods=['POST'])
@login_required
def get_genius_recommendations():
    """Get genius recommendations based on job requirements"""
    if session.get('user_type') != 'client':
        return jsonify(success=False, error="Only clients can get recommendations")

    try:
        data = request.get_json()
        if not data:
            return jsonify(success=False, error="No data received")

        job_category = data.get('category', '')
        job_skills = data.get('skills', [])
        experience_level = data.get('experience_level', '')
        job_specialty = data.get('specialty', '')

        print(f"🔍 Recommendation request - Category: {job_category}, Skills: {job_skills}, Experience: {experience_level}")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True, buffered=True)

        # Get all geniuses from approve_genius table
        base_query = """
            SELECT id, first_name, last_name, position, expertise,
                   introduction, professional_sum, profile_photo, country,
                   hourly_rate, availability
            FROM approve_genius
            WHERE 1=1
        """

        cursor.execute(base_query)
        all_geniuses = cursor.fetchall()

        print(f"📊 Found {len(all_geniuses)} total geniuses in database")

        recommendations = []

        for genius in all_geniuses:
            match_score = 0
            match_reasons = []

            # 1. Position/Category matching (30% weight)
            if job_category and genius['position']:
                position_lower = genius['position'].lower()
                category_lower = job_category.lower()

                # Direct category matches
                if any(keyword in position_lower for keyword in ['web', 'frontend', 'backend', 'fullstack']) and 'web' in category_lower:
                    match_score += 30
                    match_reasons.append("Web development expertise")
                elif any(keyword in position_lower for keyword in ['design', 'ui', 'ux', 'graphic']) and 'design' in category_lower:
                    match_score += 30
                    match_reasons.append("Design expertise")
                elif any(keyword in position_lower for keyword in ['mobile', 'app', 'ios', 'android']) and 'mobile' in category_lower:
                    match_score += 30
                    match_reasons.append("Mobile development expertise")
                elif any(keyword in position_lower for keyword in ['data', 'analyst', 'science']) and 'data' in category_lower:
                    match_score += 30
                    match_reasons.append("Data analysis expertise")
                elif any(keyword in position_lower for keyword in ['marketing', 'seo', 'social']) and 'marketing' in category_lower:
                    match_score += 30
                    match_reasons.append("Marketing expertise")
                elif any(keyword in position_lower for keyword in ['writer', 'content', 'copywriter']) and 'writing' in category_lower:
                    match_score += 30
                    match_reasons.append("Writing expertise")
                else:
                    # Partial matches
                    common_words = set(position_lower.split()) & set(category_lower.split())
                    if common_words:
                        match_score += 15
                        match_reasons.append("Related field experience")

            # 2. Experience level matching (25% weight)
            if experience_level and genius['expertise']:
                genius_exp = genius['expertise'].lower()
                job_exp = experience_level.lower()

                if genius_exp == job_exp:
                    match_score += 25
                    match_reasons.append(f"Perfect {job_exp} level match")
                elif (job_exp == 'entry' and genius_exp in ['intermediate', 'expert']) or \
                     (job_exp == 'intermediate' and genius_exp == 'expert'):
                    match_score += 20
                    match_reasons.append("Higher experience level")
                elif (job_exp == 'expert' and genius_exp == 'intermediate'):
                    match_score += 15
                    match_reasons.append("Strong experience level")

            # 3. Skills matching (35% weight) - using position and introduction for skill inference
            if job_skills:
                genius_text = f"{genius.get('position', '')} {genius.get('introduction', '')}".lower()

                if genius_text.strip():
                    job_skills_lower = [skill.lower() for skill in job_skills]

                    # Count skill mentions in genius profile text
                    skill_matches = 0
                    matched_skills = []

                    for job_skill in job_skills_lower:
                        if job_skill in genius_text:
                            skill_matches += 1
                            matched_skills.append(job_skill)

                    # Calculate skills score based on matches
                    if skill_matches > 0:
                        total_job_skills = len(job_skills)
                        skills_match_ratio = skill_matches / total_job_skills
                        skills_score = min(35, int(skills_match_ratio * 35))
                        match_score += skills_score

                        match_reasons.append(f"{skill_matches} relevant skills found")

            # 4. Specialty matching (10% weight)
            if job_specialty and genius['position']:
                if job_specialty.lower() in genius['position'].lower():
                    match_score += 10
                    match_reasons.append("Specialty match")

            # Only include geniuses with a reasonable match score
            if match_score >= 20:  # Minimum 20% match
                # Prepare genius data using correct field names
                genius_data = {
                    'id': genius['id'],
                    'first_name': genius['first_name'],
                    'last_name': genius['last_name'],
                    'position': genius['position'],
                    'expertise_level': genius['expertise'],
                    'introduction': genius['introduction'],
                    'professional_summary': genius['professional_sum'],
                    'profile_photo': genius['profile_photo'],
                    'country': genius['country'],
                    'skills': None,  # No skills field in approve_genius table
                    'portfolio_links': None,  # No portfolio_links field
                    'hourly_rate': genius['hourly_rate'],
                    'availability': genius['availability'],
                    'match_score': min(100, match_score),  # Cap at 100%
                    'match_reasons': match_reasons[:3]  # Top 3 reasons
                }

                recommendations.append(genius_data)

        # Sort by match score (highest first)
        recommendations.sort(key=lambda x: x['match_score'], reverse=True)

        # Limit to top 12 recommendations
        recommendations = recommendations[:12]

        print(f"✅ Found {len(recommendations)} recommendations out of {len(all_geniuses)} total geniuses")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_count': len(recommendations)
        })

    except Exception as e:
        print(f"❌ Error getting genius recommendations: {e}")
        traceback.print_exc()
        return jsonify(success=False, error="An error occurred while fetching recommendations")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@app.route('/api/fetch_by_expertise', methods=['POST'])
@login_required
def fetch_by_expertise():
    """Simple API to fetch geniuses by exact expertise level match"""
    try:
        data = request.get_json()
        if not data:
            return jsonify(success=False, error="No data received")

        expertise_level = data.get('expertise', '').lower()

        if not expertise_level:
            return jsonify(success=False, error="Expertise level is required")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Simple query based on expertise level
        if expertise_level == 'all':
            query = """
                SELECT id, first_name, last_name, position, expertise,
                       introduction, professional_sum, country, hourly_rate, availability
                FROM approve_genius
                ORDER BY expertise, first_name
            """
            query_info = "SELECT * FROM approve_genius ORDER BY expertise, first_name"
            cursor.execute(query)
        else:
            query = """
                SELECT id, first_name, last_name, position, expertise,
                       introduction, professional_sum, country, hourly_rate, availability
                FROM approve_genius
                WHERE LOWER(expertise) = %s
                ORDER BY first_name
            """
            query_info = f"SELECT * FROM approve_genius WHERE LOWER(expertise) = '{expertise_level}'"
            cursor.execute(query, (expertise_level,))

        geniuses = cursor.fetchall()

        print(f"🔍 Expertise search: '{expertise_level}' -> Found {len(geniuses)} geniuses")

        return jsonify({
            'success': True,
            'geniuses': geniuses,
            'total_count': len(geniuses),
            'expertise_searched': expertise_level,
            'query_info': query_info
        })

    except Exception as e:
        print(f"❌ Error fetching by expertise: {e}")
        traceback.print_exc()
        return jsonify(success=False, error=f"Database error: {str(e)}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@app.route('/api/enhanced_recommendations', methods=['POST'])
@login_required
def enhanced_recommendations():
    """Enhanced API to fetch geniuses by expertise, skills, and keywords"""
    try:
        data = request.get_json()
        if not data:
            return jsonify(success=False, error="No data received")

        expertise_level = data.get('expertise', '').lower()
        skills_input = data.get('skills', '')
        category = data.get('category', '')

        if not expertise_level:
            return jsonify(success=False, error="Expertise level is required")

        # Parse skills/keywords
        skills_list = []
        if skills_input:
            skills_list = [skill.strip().lower() for skill in skills_input.split(',') if skill.strip()]

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get all geniuses
        query = """
            SELECT id, first_name, last_name, position, expertise,
                   introduction, professional_sum, country, hourly_rate, availability
            FROM approve_genius
            ORDER BY first_name
        """
        cursor.execute(query)
        all_geniuses = cursor.fetchall()

        recommendations = []

        for genius in all_geniuses:
            match_score = 0
            match_reasons = []

            # 1. Expertise Level Matching (40% weight)
            if genius['expertise']:
                genius_exp = genius['expertise'].lower()
                if genius_exp == expertise_level:
                    match_score += 40
                    match_reasons.append(f"Perfect {expertise_level} expertise match")
                elif (expertise_level == 'entry' and genius_exp in ['intermediate', 'expert']) or \
                     (expertise_level == 'intermediate' and genius_exp == 'expert'):
                    match_score += 30
                    match_reasons.append(f"Higher {genius_exp} expertise level")
                elif (expertise_level == 'expert' and genius_exp == 'intermediate'):
                    match_score += 25
                    match_reasons.append(f"Strong {genius_exp} expertise level")

            # 2. Skills/Keywords Matching (40% weight)
            if skills_list:
                # Combine all text fields for searching
                search_text = f"{genius.get('position', '')} {genius.get('introduction', '')} {genius.get('professional_sum', '')}".lower()

                matched_skills = []
                for skill in skills_list:
                    if skill in search_text:
                        matched_skills.append(skill)

                if matched_skills:
                    # Calculate skills score based on percentage of matched skills
                    skills_match_ratio = len(matched_skills) / len(skills_list)
                    skills_score = int(skills_match_ratio * 40)
                    match_score += skills_score

                    if len(matched_skills) == 1:
                        match_reasons.append(f"Has '{matched_skills[0]}' skill")
                    else:
                        match_reasons.append(f"Has {len(matched_skills)} matching skills: {', '.join(matched_skills[:3])}")

            # 3. Category/Position Matching (20% weight)
            if category and genius['position']:
                position_lower = genius['position'].lower()
                category_lower = category.lower()

                # Direct category matches
                category_matches = {
                    'web development': ['web', 'frontend', 'backend', 'fullstack', 'developer'],
                    'mobile development': ['mobile', 'app', 'ios', 'android', 'react native', 'flutter'],
                    'design': ['design', 'ui', 'ux', 'graphic', 'visual'],
                    'digital marketing': ['marketing', 'seo', 'social', 'ads', 'content'],
                    'data science': ['data', 'analyst', 'science', 'analytics', 'statistics'],
                    'writing & content': ['writer', 'content', 'copywriter', 'blogger']
                }

                category_keywords = category_matches.get(category_lower, [])
                if any(keyword in position_lower for keyword in category_keywords):
                    match_score += 20
                    match_reasons.append(f"Position matches {category} category")
                elif any(word in position_lower for word in category_lower.split()):
                    match_score += 10
                    match_reasons.append("Related position")

            # Only include geniuses with reasonable match scores
            if match_score >= 25:  # Minimum 25% match
                genius_data = {
                    'id': genius['id'],
                    'first_name': genius['first_name'],
                    'last_name': genius['last_name'],
                    'position': genius['position'],
                    'expertise': genius['expertise'],
                    'introduction': genius['introduction'],
                    'professional_sum': genius['professional_sum'],
                    'country': genius['country'],
                    'hourly_rate': genius['hourly_rate'],
                    'availability': genius['availability'],
                    'match_score': min(100, match_score),  # Cap at 100%
                    'match_reasons': match_reasons[:4]  # Top 4 reasons
                }

                recommendations.append(genius_data)

        # Sort by match score (highest first)
        recommendations.sort(key=lambda x: x['match_score'], reverse=True)

        # Limit to top 15 recommendations
        recommendations = recommendations[:15]

        print(f"🎯 Enhanced search: expertise='{expertise_level}', skills={skills_list} -> Found {len(recommendations)} matches")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'search_criteria': {
                'expertise': expertise_level,
                'skills': skills_list,
                'category': category
            }
        })

    except Exception as e:
        print(f"❌ Error in enhanced recommendations: {e}")
        traceback.print_exc()
        return jsonify(success=False, error=f"Database error: {str(e)}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

def is_contextually_relevant(keyword, search_text, category):
    """
    Smart context checking to avoid false positives with ambiguous words
    """
    # Define ambiguous words and their contexts
    ambiguous_contexts = {
        'application': {
            'mobile development': ['mobile app', 'app development', 'ios app', 'android app', 'app store'],
            'web development': ['web app', 'web application', 'application development'],
            'negative': ['job application', 'apply for', 'application form', 'application process']
        },
        'app': {
            'mobile development': ['mobile app', 'ios app', 'android app', 'app development', 'app store'],
            'web development': ['web app', 'single page app', 'progressive web app'],
            'negative': ['job app', 'application form']
        },
        'development': {
            'web development': ['web development', 'frontend development', 'backend development'],
            'mobile development': ['mobile development', 'app development', 'ios development'],
            'negative': ['personal development', 'business development', 'career development']
        },
        'design': {
            'design': ['ui design', 'ux design', 'graphic design', 'web design', 'visual design'],
            'negative': ['curriculum design', 'business design', 'process design']
        }
    }

    # If keyword is not ambiguous, it's always relevant
    if keyword not in ambiguous_contexts:
        return True

    contexts = ambiguous_contexts[keyword]

    # Check for negative contexts first (these invalidate the match)
    for negative_phrase in contexts.get('negative', []):
        if negative_phrase in search_text:
            return False

    # Check for positive contexts for the specific category
    positive_contexts = contexts.get(category, [])
    if positive_contexts:
        for positive_phrase in positive_contexts:
            if positive_phrase in search_text:
                return True
        # If we have positive contexts but none match, it's not relevant
        return False

    # If no specific contexts defined for this category, assume it's relevant
    return True

@app.route('/api/smart_recommendations', methods=['POST'])
@login_required
def smart_recommendations():
    """Smart API to match geniuses based on job category and expertise only"""
    try:
        data = request.get_json()
        if not data:
            return jsonify(success=False, error="No data received")

        expertise_level = data.get('expertise', '').lower()
        category = data.get('category', '')

        if not expertise_level or not category:
            return jsonify(success=False, error="Expertise level and category are required")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Get ALL geniuses first (don't filter by expertise initially)
        query = """
            SELECT id, first_name, last_name, position, expertise,
                   introduction, professional_sum, country, hourly_rate, availability
            FROM approve_genius
            ORDER BY first_name
        """
        cursor.execute(query)
        all_geniuses = cursor.fetchall()

        # Define smart category keywords with context-aware matching
        category_keywords = {
            'web development': {
                'primary': ['web developer', 'frontend developer', 'backend developer', 'fullstack developer', 'full-stack developer'],
                'technologies': ['javascript', 'react', 'vue', 'angular', 'node.js', 'nodejs', 'html', 'css', 'php', 'python web', 'django', 'flask', 'laravel', 'wordpress'],
                'contexts': ['web development', 'web design', 'website', 'web application', 'web app']
            },
            'mobile development': {
                'primary': ['mobile developer', 'app developer', 'ios developer', 'android developer', 'mobile app developer'],
                'technologies': ['react native', 'flutter', 'swift', 'kotlin', 'xamarin', 'ionic', 'cordova', 'mobile app development'],
                'contexts': ['mobile development', 'app development', 'mobile application development', 'ios app development', 'android app development', 'mobile apps', 'app store']
            },
            'design': {
                'primary': ['ui designer', 'ux designer', 'graphic designer', 'visual designer', 'product designer', 'web designer'],
                'technologies': ['figma', 'photoshop', 'illustrator', 'sketch', 'adobe', 'canva', 'indesign'],
                'contexts': ['ui/ux', 'user interface', 'user experience', 'graphic design', 'visual design']
            },
            'digital marketing': {
                'primary': ['digital marketer', 'seo specialist', 'social media manager', 'marketing specialist', 'content marketer'],
                'technologies': ['google ads', 'facebook ads', 'instagram marketing', 'google analytics', 'seo tools', 'mailchimp'],
                'contexts': ['digital marketing', 'online marketing', 'social media marketing', 'search engine optimization', 'content marketing']
            },
            'data science': {
                'primary': ['data scientist', 'data analyst', 'business analyst', 'research analyst', 'statistician'],
                'technologies': ['python data', 'r programming', 'sql', 'tableau', 'power bi', 'excel analytics', 'machine learning', 'data visualization'],
                'contexts': ['data science', 'data analysis', 'business intelligence', 'statistical analysis', 'data mining']
            },
            'writing & content': {
                'primary': ['content writer', 'copywriter', 'blogger', 'technical writer', 'journalist', 'editor'],
                'technologies': ['wordpress writing', 'seo writing', 'content management', 'proofreading', 'editing'],
                'contexts': ['content writing', 'copywriting', 'blog writing', 'article writing', 'content creation']
            }
        }

        # Get smart keywords for the selected category
        selected_category_data = category_keywords.get(category.lower(), {})
        if not selected_category_data:
            return jsonify(success=False, error=f"Category '{category}' not supported")

        recommendations = []

        for genius in all_geniuses:
            match_score = 0
            match_reasons = []

            # Combine all text fields for searching (excluding title)
            search_text = f"{genius.get('position', '')} {genius.get('introduction', '')} {genius.get('professional_sum', '')}".lower()

            # 1. PRIORITY: Smart Category/Skills Matching (70% weight)
            category_matches = []
            match_types = []

            # Check primary job titles (highest weight) with context awareness
            for primary_title in selected_category_data.get('primary', []):
                if primary_title.lower() in search_text:
                    if is_contextually_relevant(primary_title.lower(), search_text, category.lower()):
                        category_matches.append(primary_title)
                        match_types.append('primary')

            # Check specific technologies (medium weight) with context awareness
            for tech in selected_category_data.get('technologies', []):
                if tech.lower() in search_text:
                    if is_contextually_relevant(tech.lower(), search_text, category.lower()):
                        category_matches.append(tech)
                        match_types.append('technology')

            # Check context phrases (lower weight but still relevant) with context awareness
            for context in selected_category_data.get('contexts', []):
                if context.lower() in search_text:
                    if is_contextually_relevant(context.lower(), search_text, category.lower()):
                        category_matches.append(context)
                        match_types.append('context')

            # Only proceed if there are category matches
            if category_matches:
                # Calculate smart category match score (up to 70 points)
                primary_count = match_types.count('primary')
                tech_count = match_types.count('technology')
                context_count = match_types.count('context')

                # Weighted scoring: primary=30, technology=20, context=10 (max 70)
                category_score = min(70, (primary_count * 30) + (tech_count * 20) + (context_count * 10))
                match_score += category_score

                if len(category_matches) == 1:
                    match_reasons.append(f"Has '{category_matches[0]}' skill")
                elif len(category_matches) <= 3:
                    match_reasons.append(f"Skills: {', '.join(category_matches[:3])}")
                else:
                    match_reasons.append(f"Strong {category} background ({len(category_matches)} skills)")

                # 2. SECONDARY: Expertise Level Matching (30% weight)
                if genius['expertise']:
                    genius_exp = genius['expertise'].lower()
                    if genius_exp == expertise_level:
                        match_score += 30
                        match_reasons.append(f"Perfect {expertise_level} expertise")
                    elif (expertise_level == 'entry' and genius_exp in ['intermediate', 'expert']) or \
                         (expertise_level == 'intermediate' and genius_exp == 'expert'):
                        match_score += 20
                        match_reasons.append(f"Higher {genius_exp} expertise")
                    elif (expertise_level == 'expert' and genius_exp == 'intermediate'):
                        match_score += 15
                        match_reasons.append(f"Strong {genius_exp} expertise")
                    elif (expertise_level == 'intermediate' and genius_exp == 'entry'):
                        match_score += 10
                        match_reasons.append(f"Entry level but trainable")

                # Only include if minimum score is met (at least some category match)
                if match_score >= 20:  # Minimum threshold
                    genius_data = {
                        'id': genius['id'],
                        'first_name': genius['first_name'],
                        'last_name': genius['last_name'],
                        'position': genius['position'],
                        'expertise': genius['expertise'],
                        'introduction': genius['introduction'],
                        'professional_sum': genius['professional_sum'],
                        'country': genius['country'],
                        'hourly_rate': genius['hourly_rate'],
                        'availability': genius['availability'],
                        'match_score': min(100, match_score),
                        'match_reasons': match_reasons[:3]  # Top 3 reasons
                    }

                    recommendations.append(genius_data)

        # If no category matches found, try fallback options
        if not recommendations:
            print(f"⚠️ No category matches found for '{category}' + '{expertise_level}'. Trying fallbacks...")

            # Fallback 1: Same expertise, any category
            fallback_recommendations = []
            for genius in all_geniuses:
                # Only include geniuses with matching expertise for fallback
                if genius['expertise'] and genius['expertise'].lower() == expertise_level:
                    genius_data = {
                        'id': genius['id'],
                        'first_name': genius['first_name'],
                        'last_name': genius['last_name'],
                        'position': genius['position'],
                        'expertise': genius['expertise'],
                        'introduction': genius['introduction'],
                        'professional_sum': genius['professional_sum'],
                        'country': genius['country'],
                        'hourly_rate': genius['hourly_rate'],
                        'availability': genius['availability'],
                        'match_score': 50,  # Base expertise match only
                        'match_reasons': [f"Has {expertise_level} expertise level", "Different category but same skill level"]
                    }
                    fallback_recommendations.append(genius_data)

            # Limit fallback to 8 results
            fallback_recommendations = fallback_recommendations[:8]

            return jsonify({
                'success': True,
                'recommendations': fallback_recommendations,
                'total_count': len(fallback_recommendations),
                'is_fallback': True,
                'fallback_type': 'expertise_only',
                'message': f"No exact category matches found. Showing {expertise_level} level geniuses from other categories.",
                'search_criteria': {
                    'expertise': expertise_level,
                    'category': category,
                    'keywords_used': list(selected_category_data.get('primary', []))[:5] + list(selected_category_data.get('technologies', []))[:5]
                }
            })

        # Sort by match score (highest first)
        recommendations.sort(key=lambda x: x['match_score'], reverse=True)

        # Limit to top 12 recommendations
        recommendations = recommendations[:12]

        print(f"🎯 Smart matching: category='{category}', expertise='{expertise_level}' -> Found {len(recommendations)} matches")

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total_count': len(recommendations),
            'is_fallback': False,
            'search_criteria': {
                'expertise': expertise_level,
                'category': category,
                'keywords_used': list(selected_category_data.get('primary', []))[:5] + list(selected_category_data.get('technologies', []))[:5]
            }
        })

    except Exception as e:
        print(f"❌ Error in smart recommendations: {e}")
        traceback.print_exc()
        return jsonify(success=False, error=f"Database error: {str(e)}")
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == '__main__':
    print("🚀 Starting Genius Recommendations Test App...")
    print("📊 Database: giggenius (production)")
    print("🔗 URL: http://127.0.0.1:5002")
    print("=" * 50)
    app.run(debug=True, port=5002, host='0.0.0.0')
