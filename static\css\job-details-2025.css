/* Futuristic Job Details Styling for 2025 */
:root {
    --primary-blue: #004AAD;
    --primary-pink: #CD208B;
    --primary-gradient: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
    --secondary-blue: #0062ff;
    --accent-teal: #00d4ff;
    --accent-purple: #9d00ff;
    --surface-light: #ffffff;
    --surface-dark: #121212;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --box-shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.08);
    --box-shadow-strong: 0 15px 40px rgba(0, 0, 0, 0.12);
    --transition-smooth: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', system-ui, -apple-system, sans-serif;
}

body {
    background-color: #f8faff;
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Container Styles */
.container {
    width: 100%;
    flex: 1;
    margin: 0;
    padding: 0;
    background-color: #f8faff;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(205, 32, 139, 0.05) 0%, rgba(0, 74, 173, 0.05) 50%, rgba(248, 250, 255, 0) 70%);
    z-index: 0;
}

.container::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 74, 173, 0.05) 0%, rgba(205, 32, 139, 0.05) 50%, rgba(248, 250, 255, 0) 70%);
    z-index: 0;
}

/* Navigation Header */
.navigation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-soft);
    border: 1px solid rgba(255, 255, 255, 0.18);
    max-width: 900px;
    width: 100%;
    margin: 2rem auto 1rem auto;
}

/* Header Progress Indicator */
.header-progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin: 0 2rem;
}

.header-progress-indicator .progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 70px;
    position: relative;
    z-index: 1;
}

.header-progress-indicator .step-number {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 13px;
    background-color: #e8f0ff;
    color: var(--text-secondary);
    position: relative;
    z-index: 2;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.header-progress-indicator .step-number.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 74, 173, 0.3);
    transform: scale(1.1);
}

.header-progress-indicator .step-label {
    font-size: 11px;
    margin-top: 6px;
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
    transition: all 0.3s ease;
}

.header-progress-indicator .step-label.active {
    color: var(--primary-blue);
    font-weight: 600;
}

.header-progress-indicator .step-connector {
    height: 2px;
    width: 50px;
    background: #e8f0ff;
    position: relative;
    border-radius: 2px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(0, 74, 173, 0.2);
    border-radius: 50px;
    color: var(--primary-blue);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    font-family: 'Poppins', sans-serif;
    text-decoration: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.back-button:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    color: white;
    transform: translateX(-3px) translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 74, 173, 0.2);
}

.back-button i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.back-button:hover i {
    transform: translateX(-2px);
}

.page-title-header {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin: 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: none; /* Hide since we have progress indicator in header */
}

/* Job Details Container */
.job-details-container {
    max-width: 900px;
    width: 100%;
    margin: 2rem auto;
    padding: 2rem;
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-soft);
    border: 1px solid rgba(255, 255, 255, 0.18);
    overflow: hidden;
}

/* Responsive Header Progress Indicator */
@media (max-width: 768px) {
    .header-progress-indicator {
        margin: 0 1rem;
    }

    .header-progress-indicator .progress-step {
        width: 60px;
    }

    .header-progress-indicator .step-number {
        width: 24px;
        height: 24px;
        font-size: 11px;
    }

    .header-progress-indicator .step-label {
        font-size: 10px;
        margin-top: 4px;
    }

    .header-progress-indicator .step-connector {
        width: 35px;
        height: 2px;
    }
}

@media (max-width: 480px) {
    .navigation-header {
        padding: 1rem 1.5rem;
        flex-direction: column;
        gap: 1rem;
    }

    .header-progress-indicator {
        margin: 0;
        order: 2;
    }

    .back-button {
        align-self: flex-start;
        order: 1;
    }

    .header-progress-indicator .progress-step {
        width: 50px;
    }

    .header-progress-indicator .step-number {
        width: 22px;
        height: 22px;
        font-size: 10px;
    }

    .header-progress-indicator .step-label {
        font-size: 9px;
        margin-top: 3px;
    }

    .header-progress-indicator .step-connector {
        width: 25px;
        height: 1px;
    }
}

.job-details-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-gradient);
    z-index: 2;
}

/* Page Title */
.page-title {
    color: var(--primary-blue);
    margin-bottom: 2rem;
    font-weight: 700;
    font-size: clamp(1.8rem, 5vw, 2.5rem);
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Detail Sections */
.detail-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    background: linear-gradient(145deg, #f0f7ff, #ffffff);
    box-shadow: 8px 8px 16px rgba(174, 174, 192, 0.2),
                -8px -8px 16px rgba(255, 255, 255, 0.7);
    border: none;
    position: relative;
    overflow: hidden;
    transition: var(--transition-smooth);
}

.detail-section:hover {
    transform: translateY(-5px);
    box-shadow: 12px 12px 20px rgba(174, 174, 192, 0.3),
                -12px -12px 20px rgba(255, 255, 255, 0.8);
}

.detail-section::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.detail-section:hover::after {
    opacity: 0.15;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.section-header h2 {
    color: var(--primary-blue);
    font-weight: 600;
    margin: 0;
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-pink));
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
    border-radius: 3px;
}

@keyframes gradient-shift {
    0% {background-position: 0% 50%;}
    50% {background-position: 100% 50%;}
    100% {background-position: 0% 50%;}
}

/* Edit Buttons */
.edit-button {
    background: rgba(255, 255, 255, 0.7);
    border: none;
    color: var(--primary-blue);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 30px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.edit-button:hover {
    background: var(--primary-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 74, 173, 0.2);
}

.edit-button i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.edit-button:hover i {
    transform: rotate(15deg);
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

/* Selection Type Toggle */
.selection-type-toggle {
    display: flex;
    margin-bottom: 1rem;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1px solid var(--neutral-300);
}

.selection-toggle {
    flex: 1;
    padding: 0.75rem 1rem;
    background-color: var(--neutral-200);
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
    color: var(--text-secondary);
}

.selection-toggle:first-child {
    border-right: 1px solid var(--neutral-300);
}

.selection-toggle.active {
    background-color: var(--primary-blue);
    color: white;
}

.selection-toggle:hover:not(.active) {
    background-color: var(--neutral-300);
}

.input-help {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 8px;
    font-style: italic;
}

.form-control {
    width: 100%;
    padding: 1rem 1.2rem;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #f5f8ff;
    box-shadow: inset 2px 2px 5px rgba(0,0,0,0.05);
    color: var(--text-primary);
}

.form-control:focus {
    outline: none;
    transform: scale(1.01);
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(0, 74, 173, 0.1);
}

/* Content Display */
.detail-section p {
    font-size: 1.1rem;
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0.5rem 0;
}

/* Scope Details */
.scope-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.scope-description h3 {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.scope-meta {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    background: rgba(0, 0, 0, 0.02);
    padding: 1rem;
    border-radius: var(--border-radius-md);
}

.scope-meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-label i {
    color: var(--primary-blue);
    font-size: 1rem;
}

.meta-value {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Skills Styling */
.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 0.5rem;
}

.skill-tag {
    background: linear-gradient(135deg, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
    color: var(--primary-blue);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 74, 173, 0.2);
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(0, 74, 173, 0.2), rgba(205, 32, 139, 0.2));
}

.skills-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.skills-input-container .form-control {
    flex: 1;
}

.add-skill-btn {
    background: var(--primary-blue);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-skill-btn:hover {
    background: var(--primary-pink);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.skills-edit-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;
    min-height: 40px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-md);
    border: 1px dashed rgba(0, 0, 0, 0.1);
}

.skills-edit-list .skill-tag {
    padding-right: 8px;
    position: relative;
    cursor: pointer;
}

.skills-edit-list .skill-tag::after {
    content: '×';
    font-size: 1.2rem;
    margin-left: 6px;
    cursor: pointer;
    color: var(--primary-pink);
}

.skills-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.skill-suggestion {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-secondary);
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.skill-suggestion:hover {
    background-color: var(--primary-blue);
    color: white;
}

/* Scope Size Options */
.scope-size-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 10px;
}

.scope-size-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.scope-size-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.option-label {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: var(--border-radius-md);
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.08);
    width: 100%;
    transition: all 0.3s ease;
}

.option-label i {
    font-size: 1.2rem;
    margin-right: 12px;
    color: var(--primary-blue);
    transition: all 0.3s ease;
}

.option-label span {
    font-weight: 500;
    margin-right: 10px;
}

.option-label small {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-left: auto;
}

.scope-size-option input[type="radio"]:checked + .option-label {
    background: rgba(0, 74, 173, 0.08);
    border-color: var(--primary-blue);
    box-shadow: 0 2px 8px rgba(0, 74, 173, 0.1);
}

.scope-size-option input[type="radio"]:checked + .option-label i {
    color: var(--primary-pink);
}

.scope-size-option:hover .option-label {
    background: rgba(0, 74, 173, 0.05);
    transform: translateY(-2px);
}

/* Scope Flex Layout */
.scope-flex-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.flex-1 {
    flex: 1;
    min-width: 0;
}

/* Scope Duration */
.scope-duration {
    margin-top: 10px;
}

.duration-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.duration-input input {
    width: 80px;
    min-width: 80px;
}

.duration-input select {
    flex: 1;
}

/* Category Details */
.category-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 0.5rem;
}

.category-meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Budget Details */
.budget-details {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
}

#job-budget {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-blue);
    padding: 0.5rem 0;
}

.budget-help-text {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    font-style: italic;
}

/* Budget Fields Toggle */
#hourly-rate-fields,
#fixed-price-fields {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Initially hide hourly fields */
#hourly-rate-fields {
    display: none;
}

/* Budget Type Options */
.budget-type-options {
    display: flex;
    flex-direction: row;
    gap: 15px;
    margin-top: 10px;
}

.budget-type-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    flex: 1;
}

.budget-type-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.budget-type-option .option-label {
    justify-content: center;
    text-align: center;
    padding: 15px;
}

.budget-type-option .option-label i {
    font-size: 1.4rem;
    margin-right: 10px;
}

.budget-type-option input[type="radio"]:checked + .option-label {
    background: rgba(0, 74, 173, 0.08);
    border-color: var(--primary-blue);
    box-shadow: 0 2px 8px rgba(0, 74, 173, 0.1);
}

.budget-type-option input[type="radio"]:checked + .option-label i {
    color: var(--primary-pink);
}

.budget-type-option:hover .option-label {
    background: rgba(0, 74, 173, 0.05);
    transform: translateY(-2px);
}

/* Submit Buttons Container */
.submit-buttons {
    display: flex;
    gap: 15px;
    margin: 2rem auto 1rem;
    justify-content: center;
    width: 100%;
    max-width: 600px;
}

/* Primary Button */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex: 1;
    max-width: 300px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(205, 32, 139, 0.2);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
    transition: all 0.6s ease;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(205, 32, 139, 0.3);
}

.btn-primary:hover::before {
    left: 100%;
}

/* Secondary Button (Draft Post) */
.btn-secondary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex: 1;
    max-width: 300px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(205, 32, 139, 0.2);
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
    transition: all 0.6s ease;
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(205, 32, 139, 0.3);
}

.btn-secondary:hover::before {
    left: 100%;
}

/* Modal Styling */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 1000;
    justify-content: center;
    align-items: flex-start;
    padding: 2rem 0;
    overflow-y: auto;
    animation: modal-bg-in 0.3s ease forwards;
}

@keyframes modal-bg-in {
    from { background-color: rgba(0, 0, 0, 0); }
    to { background-color: rgba(0, 0, 0, 0.5); }
}

.modal-content {
    background-color: white;
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    box-shadow: var(--box-shadow-strong);
    position: relative;
    overflow-y: auto;
    animation: modal-in 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-top: 5px solid var(--primary-blue);
}

@keyframes modal-in {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-content h2 {
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    font-weight: 600;
    font-size: 1.5rem;
    text-align: center;
}

.modal-content button {
    margin-top: 1.5rem;
}

.modal-content button:last-child {
    background: transparent;
    border: 2px solid var(--text-secondary);
    color: var(--text-secondary);
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-content button:last-child:hover {
    background: var(--text-secondary);
    color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navigation-header {
        padding: 1rem 1.5rem;
        margin: 1rem auto 0.5rem auto;
        border-radius: var(--border-radius-md);
    }

    .back-button {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .page-title-header {
        font-size: 1.2rem;
    }

    .job-details-container {
        padding: 1.5rem;
        margin: 1rem auto;
    }

    .detail-section {
        padding: 1.2rem;
    }

    .btn-primary, .btn-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .modal-content {
        padding: 1.5rem;
        width: 95%;
    }
}

/* Animation for Job Status */
.job-status {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    position: relative;
    margin-top: 1rem;
}

.job-status.draft {
    background-color: rgba(255, 193, 7, 0.15);
    color: #ffc107;
}

.job-status.progress {
    background-color: rgba(33, 150, 243, 0.15);
    color: #2196f3;
}

.job-status.ready {
    background-color: rgba(76, 175, 80, 0.15);
    color: #4caf50;
}

.job-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.job-status.draft::before {
    background-color: #ffc107;
}

.job-status.progress::before {
    background-color: #2196f3;
}

.job-status.ready::before {
    background-color: #4caf50;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}
