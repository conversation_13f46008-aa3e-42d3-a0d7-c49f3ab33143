<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
        }
        html, body {
            height: 100%;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        /* Navbar Styles (copied from genius_page.html) */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: relative;
            z-index: 1000;
            min-height: 5rem;
        }
        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
            padding-left: 0;
            flex: 1;
        }
        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
        }
        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }
        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }
        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
            height: 100%;
            margin: 0;
        }
        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        .nav-links > a:hover, .nav-links > a.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropdown {
            position: relative;
            display: inline-block;
        }
        .nav-dropbtn {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }
        .nav-dropbtn:hover, .nav-dropbtn.active {
            color: var(--primary-pink);
            background-color: transparent;
        }
        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }
        .nav-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }
        .nav-dropdown-content a:hover {
            background-color: transparent;
            color: var(--primary-pink);
        }
        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
            justify-content: flex-end;
            height: 100%;
        }
        .search-container {
            display: flex;
            align-items: center;
            height: 100%;
        }
        .search-bar {
            display: flex;
            align-items: center;
            background: white;
            border: 2px solid var(--primary-blue);
            border-radius: 25px;
            height: 36px;
            width: 260px;
            transition: all 0.3s ease;
            position: relative;
        }
        .search-bar input {
            border: none;
            outline: none;
            padding: 0 14px;
            flex: 1;
            height: 100%;
            font-size: 0.85rem;
            background: transparent;
            color: #333;
            font-family: 'Poppins', sans-serif;
            line-height: 1;
            display: flex;
            align-items: center;
        }
        .search-bar .icon {
            color: var(--primary-blue);
            padding: 0 14px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1rem;
            height: 100%;
        }
        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .notification-icon i {
            font-size: 1.4rem;
            color: var(--primary-blue);
            transition: color 0.3s ease;
        }
        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
            position: relative;
        }
        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }
        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }
        .profile-dropdown-content a i {
            width: 16px;
            text-align: center;
        }
        .profile-dropdown-content a:hover,
        .profile-dropdown-content a:focus {
            background-color: #f9f9f9;
            color: var(--primary-pink);
            outline: none;
        }
        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }
        .logout-option {
            color: #dc3545 !important;
        }
        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }
        .profile-dropdown.active .profile-dropdown-content {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        /* Footer Styles (from landing_page.html) */
        footer {
            background: var(--primary-blue);
            padding: 2.5rem 5%;
            align-items: center;
            padding-bottom: 2rem;
        }
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
            margin-bottom: 2rem;
        }
        .footer-column h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }
        .footer-column a {
            font-size: 1.1rem;
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease; 
        }
        .footer-column a:hover {
            text-decoration: underline;
        }
        .footer-bottom {
            font-size: 1.1rem;
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid white;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rem;
        }
        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }
        .footer-bottom a:hover {
            text-decoration: underline;
        }
        .footer-bottom .social-icons img {
            width: 1rem;
            height: 1rem;
        }
        .social-icons .bi {
            font-size: 1.5rem; 
            margin-right: 10px;
            border-radius: 50%;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }
        .social-icons .bi:hover {
            transform: scale(1.2);
            color: var(--primary-pink);
        }
    </style>
</head>
<body>
    <!-- Header/Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="#" style="text-decoration: none;">
                <div class="logo">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </div>
            </a>
            <div class="nav-links" id="navLinks">
                <a href="#">Find Gigs</a>
                <a href="#">My Applications</a>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Contracts <i class="fas fa-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="#">Log Hours</a>
                        <a href="#">Work Diary</a>
                    </div>
                </div>
                <div class="nav-dropdown">
                    <button class="nav-dropbtn">Earnings <i class="fas fa-chevron-down"></i></button>
                    <div class="nav-dropdown-content">
                        <a href="#">Billings and Earnings</a>
                        <a href="#">Withdraw Earnings</a>
                        <a href="#">Tax Info</a>
                    </div>
                </div>
                <a href="#">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="search-container">
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search...">
                    <i class="fas fa-search icon"></i>
                </div>
            </div>
            <div class="auth-buttons">
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                    <span id="notification-count">0</span>
                </div>
                <div class="profile-dropdown">
                    <div class="profile-button">
                        <img src="{{ url_for('static', filename='img/default-avatar.png') }}" alt="Profile Picture">
                    </div>
                    <div class="profile-dropdown-content">
                        <a href="#"><i class="fas fa-user"></i> My Profile</a>
                        <a href="#"><i class="fas fa-cog"></i> Account Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="logout-option" style="color: #dc3545 !important; font-weight: bold;"><i class="fas fa-sign-out-alt"></i> Log Out</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- Main Content Placeholder -->
    <main style="flex:1;display:flex;align-items:center;justify-content:center;min-height:400px;">
        <h2>Sample Main Content</h2>
    </main>
    <!-- Footer -->
    <footer>
        <div class="footer-grid">
            <div class="footer-column">
                <h3>For Clients</h3>
                <div class="footer-links">
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>For Geniuses</h3>
                <div class="footer-links">
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Resources</h3>
                <div class="footer-links">
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
            </div>
            <div class="footer-column">
                <h3>Company</h3>
                <div class="footer-links">
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>Follow Us:
                <span class="social-icons">
                    <a href="#" class="bi bi-facebook"></a>
                    <a href="#" class="bi bi-instagram"></a>
                    <a href="#" class="bi bi-threads"></a>
                    <a href="#" class="bi bi-twitter-x"></a>
                    <a href="#" class="bi bi-tiktok"></a>
                    <a href="#" class="bi bi-youtube"></a>
                    <a href="#" class="bi bi-linkedin"></a>
                </span>
            </p>
            <p>©2025 GigGenius by <a href="#">Genuinely Business Solutions</a></p>
            <p>
                <a href="#">Terms of Service</a> | 
                <a href="#">Privacy Policy</a>
            </p>
        </div>
    </footer>
</body>
</html>
