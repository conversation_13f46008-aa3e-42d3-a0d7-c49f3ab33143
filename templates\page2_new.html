<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Job Post - Step 2</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="{{ url_for('static', filename='css/page2-2025.css') }}">
  <style>
    /* Navigation Header Styles */
    .navigation-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
        background-color: #ffffff;
    }

    /* Progress Indicator Styles */
    .progress-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        margin: 0 20px;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 80px;
        position: relative;
        z-index: 1;
    }

    .step-number {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 13px;
        background-color: #e5e7eb;
        color: #6b7280;
        position: relative;
        z-index: 2;
        border: 2px solid white;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .step-number.active {
        background-color: #2563eb;
        color: white;
    }

    .step-label {
        font-size: 12px;
        margin-top: 4px;
        font-weight: 500;
        color: #6b7280;
    }

    .step-label.active {
        color: #2563eb;
        font-weight: 600;
    }

    .step-connector {
        height: 3px;
        width: 60px;
        background-color: #e5e7eb;
        position: relative;
    }

    /* Back Button Styles */
    .back-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .back-button:hover {
        background-color: #f1f5f9;
        border-color: #d1d5db;
        color: #1f2937;
    }

    .back-button i {
        font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Navigation Header with Progress Indicator -->
    <div class="navigation-header">
      <button type="button" class="back-button" onclick="goBackToPage1()">
        <i class="fas fa-arrow-left"></i>
        <span>Back</span>
      </button>

      <!-- Progress Indicator -->
      <div class="progress-indicator">
          <div class="progress-step">
              <div class="step-number">1</div>
              <div class="step-label">Job Type</div>
          </div>
          <div class="step-connector"></div>
          <div class="progress-step">
              <div class="step-number active">2</div>
              <div class="step-label active">Category</div>
          </div>
          <div class="step-connector"></div>
          <div class="progress-step">
              <div class="step-number">3</div>
              <div class="step-label">Review & Post</div>
          </div>
      </div>
    </div>

    <!-- Main content wrapper -->
    <div class="main-content">
      <!-- Left side -->
      <div class="left-side">
      <div class="step"><i class="fas fa-pencil-alt"></i> &nbsp; <strong>Job post</strong></div>
      <div class="title">Let's start with a strong title.</div>
      <div class="description">
        This helps your job post stand out to the right candidates. It's the first thing they'll see, so make it count!
      </div>

      <div class="examples">
        <strong><i class="fas fa-lightbulb"></i> Pro Tips</strong>
        <ul>
          <li>Be specific about the skills you're looking for</li>
          <li>Mention the project type or industry</li>
          <li>Keep it concise but descriptive</li>
        </ul>
      </div>
    </div>

    <!-- Right side -->
    <div class="right-side">
      <label for="job-title"><strong>Write a title for your job post</strong></label>
      <input type="text" id="job-title" placeholder="e.g. UX Designer for mobile app" oninput="handleTitleInput(this.value)">

      <div class="examples">
        <strong>Example titles</strong>
        <ul>
          <li>UX/UI designer to bring website mockup and prototype to life</li>
          <li>Video editor needed to create whiteboard explainer video</li>
          <li>UX designer with e-commerce experience to support app development</li>
        </ul>
      </div>

      <div class="job-category">
        <div class="category-header">
          <strong>Job category & specialty</strong>
          <span class="required-badge">Required</span>
        </div>
        <div id="selected-category-container">
          <div id="selected-category-info">
            <p id="selected-category-text">No category selected</p>
            <p id="selected-specialty-text" class="specialty-text">No specialty selected</p>
          </div>
        </div>
        <div class="category-actions">
          <div class="see-all" onclick="openCategoryModal()"><i class="fas fa-list"></i> Choose category & specialty</div>
          <button class="next-button" onclick="goToPage3()">Next <i class="fas fa-arrow-right"></i></button>
        </div>

    </div> <!-- End right-side -->
    </div> <!-- End main-content -->
  </div> <!-- End container -->

  <!-- Category Modal -->
  <div id="categoryModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeCategoryModal()">&times;</span>
      <h2>Select Category & Specialty</h2>

      <!-- Category Selection -->
      <div class="form-group">
        <label for="category-selection-type">Category Selection</label>
        <div class="selection-type-toggle">
          <button type="button" class="selection-toggle active" onclick="toggleCategorySelectionType('predefined')">Choose from list</button>
          <button type="button" class="selection-toggle" onclick="toggleCategorySelectionType('custom')">Enter custom</button>
        </div>
      </div>

      <!-- Predefined Category Selection -->
      <div id="predefined-category" class="form-group">
        <label for="categoryDropdown">Job Category <span class="required">*</span></label>
        <select id="categoryDropdown" class="form-control" onchange="updateSpecialties()">
          <option value="" disabled selected>Select a category</option>
          <option value="Accounting & Consulting">Accounting & Consulting</option>
          <option value="Admin Support">Admin Support</option>
          <option value="Customer Service">Customer Service</option>
          <option value="Data Science & Analytics">Data Science & Analytics</option>
          <option value="Design & Creative">Design & Creative</option>
          <option value="Engineering & Architecture">Engineering & Architecture</option>
          <option value="IT & Networking">IT & Networking</option>
          <option value="Legal">Legal</option>
          <option value="Sales & Marketing">Sales & Marketing</option>
          <option value="Translation">Translation</option>
          <option value="Web, Mobile & Software Dev">Web, Mobile & Software Dev</option>
          <option value="Writing">Writing</option>
        </select>
      </div>

      <!-- Custom Category Input -->
      <div id="custom-category" class="form-group" style="display: none;">
        <label for="custom-category-input">Custom Job Category <span class="required">*</span></label>
        <input type="text" id="custom-category-input" class="form-control" placeholder="Enter your own category">
      </div>

      <!-- Specialty Selection -->
      <div class="form-group">
        <label for="specialty-selection-type">Specialty Selection</label>
        <div class="selection-type-toggle">
          <button type="button" class="selection-toggle active" onclick="toggleSpecialtySelectionType('predefined')">Choose from list</button>
          <button type="button" class="selection-toggle" onclick="toggleSpecialtySelectionType('custom')">Enter custom</button>
        </div>
      </div>

      <!-- Predefined Specialty Selection -->
      <div id="predefined-specialty" class="form-group">
        <label for="specialtyDropdown">Specialty <span class="optional">(Optional)</span></label>
        <select id="specialtyDropdown" class="form-control">
          <!-- Design & Creative Specialties -->
          <optgroup label="Design & Creative" class="specialty-group" data-category="Design & Creative">
            <option value="none">None</option>
            <option value="Brand Identity Design">Brand Identity Design</option>
            <option value="Logo Design">Logo Design</option>
            <option value="Graphic Design">Graphic Design</option>
            <option value="UI/UX Design">UI/UX Design</option>
            <option value="Illustration">Illustration</option>
            <option value="Video Editing">Video Editing</option>
            <option value="Animation">Animation</option>
            <option value="Voice Talent">Voice Talent</option>
          </optgroup>

          <!-- Translation Specialties -->
          <optgroup label="Translation" class="specialty-group" data-category="Translation">
            <option value="none">None</option>
            <option value="Document Translation">Document Translation</option>
            <option value="Website Localization">Website Localization</option>
            <option value="Technical Translation">Technical Translation</option>
            <option value="Legal Translation">Legal Translation</option>
            <option value="Medical Translation">Medical Translation</option>
          </optgroup>

          <!-- Marketing Specialties -->
          <optgroup label="Sales & Marketing" class="specialty-group" data-category="Sales & Marketing">
            <option value="none">None</option>
            <option value="Social Media Marketing">Social Media Marketing</option>
            <option value="Content Marketing">Content Marketing</option>
            <option value="Email Marketing">Email Marketing</option>
            <option value="SEO">SEO</option>
            <option value="PPC Advertising">PPC Advertising</option>
          </optgroup>

          <!-- Web Development Specialties -->
          <optgroup label="Web, Mobile & Software Dev" class="specialty-group" data-category="Web, Mobile & Software Dev">
            <option value="none">None</option>
            <option value="Frontend Development">Frontend Development</option>
            <option value="Backend Development">Backend Development</option>
            <option value="Full Stack Development">Full Stack Development</option>
            <option value="Mobile App Development">Mobile App Development</option>
            <option value="WordPress Development">WordPress Development</option>
          </optgroup>

          <!-- Writing Specialties -->
          <optgroup label="Writing" class="specialty-group" data-category="Writing">
            <option value="none">None</option>
            <option value="Blog Writing">Blog Writing</option>
            <option value="Copywriting">Copywriting</option>
            <option value="Technical Writing">Technical Writing</option>
            <option value="Creative Writing">Creative Writing</option>
            <option value="SEO Writing">SEO Writing</option>
          </optgroup>

          <!-- Accounting & Consulting Specialties -->
          <optgroup label="Accounting & Consulting" class="specialty-group" data-category="Accounting & Consulting">
            <option value="none">None</option>
            <option value="Bookkeeping">Bookkeeping</option>
            <option value="Financial Analysis">Financial Analysis</option>
            <option value="Tax Preparation">Tax Preparation</option>
            <option value="Business Consulting">Business Consulting</option>
            <option value="Financial Planning">Financial Planning</option>
          </optgroup>

          <!-- Admin Support Specialties -->
          <optgroup label="Admin Support" class="specialty-group" data-category="Admin Support">
            <option value="none">None</option>
            <option value="Virtual Assistant">Virtual Assistant</option>
            <option value="Data Entry">Data Entry</option>
            <option value="Transcription">Transcription</option>
            <option value="Email Management">Email Management</option>
            <option value="Calendar Management">Calendar Management</option>
          </optgroup>

          <!-- Customer Service Specialties -->
          <optgroup label="Customer Service" class="specialty-group" data-category="Customer Service">
            <option value="none">None</option>
            <option value="Customer Support">Customer Support</option>
            <option value="Technical Support">Technical Support</option>
            <option value="Chat Support">Chat Support</option>
            <option value="Phone Support">Phone Support</option>
            <option value="Email Support">Email Support</option>
          </optgroup>

          <!-- Data Science & Analytics Specialties -->
          <optgroup label="Data Science & Analytics" class="specialty-group" data-category="Data Science & Analytics">
            <option value="none">None</option>
            <option value="Data Analysis">Data Analysis</option>
            <option value="Machine Learning">Machine Learning</option>
            <option value="Data Visualization">Data Visualization</option>
            <option value="Statistical Analysis">Statistical Analysis</option>
            <option value="Business Intelligence">Business Intelligence</option>
          </optgroup>

          <!-- Engineering & Architecture Specialties -->
          <optgroup label="Engineering & Architecture" class="specialty-group" data-category="Engineering & Architecture">
            <option value="none">None</option>
            <option value="Mechanical Engineering">Mechanical Engineering</option>
            <option value="Civil Engineering">Civil Engineering</option>
            <option value="Electrical Engineering">Electrical Engineering</option>
            <option value="Architectural Design">Architectural Design</option>
            <option value="CAD Design">CAD Design</option>
          </optgroup>

          <!-- IT & Networking Specialties -->
          <optgroup label="IT & Networking" class="specialty-group" data-category="IT & Networking">
            <option value="none">None</option>
            <option value="Network Administration">Network Administration</option>
            <option value="System Administration">System Administration</option>
            <option value="DevOps">DevOps</option>
            <option value="Cloud Engineering">Cloud Engineering</option>
            <option value="IT Security">IT Security</option>
          </optgroup>

          <!-- Legal Specialties -->
          <optgroup label="Legal" class="specialty-group" data-category="Legal">
            <option value="none">None</option>
            <option value="Contract Law">Contract Law</option>
            <option value="Legal Writing">Legal Writing</option>
            <option value="Paralegal Services">Paralegal Services</option>
            <option value="Legal Research">Legal Research</option>
            <option value="Patent Law">Patent Law</option>
          </optgroup>

          <!-- Other categories -->
          <optgroup label="Other Categories" class="specialty-group" data-category="other">
            <option value="none">None</option>
            <option value="General">General</option>
          </optgroup>
        </select>
      </div>

      <!-- Custom Specialty Input -->
      <div id="custom-specialty" class="form-group" style="display: none;">
        <label for="custom-specialty-input">Custom Specialty <span class="required">*</span></label>
        <input type="text" id="custom-specialty-input" class="form-control" placeholder="Enter your own specialty">
      </div>

      <div class="modal-buttons">
        <button onclick="closeCategoryModal()">Cancel</button>
        <button onclick="applyCategory()">Apply</button>
      </div>
    </div>
  </div>

  <script>
    // Store selected category and specialty
    let selectedCategory = '';
    let selectedSpecialty = '';

    // Back button functionality
    function goBackToPage1() {
      // Check if user has entered any data
      const jobTitle = document.getElementById('job-title').value.trim();
      const hasSelectedCategory = selectedCategory && selectedCategory.length > 0;

      if (jobTitle || hasSelectedCategory) {
        if (confirm('Are you sure you want to go back? Any unsaved changes will be lost.')) {
          window.location.href = '/page1';
        }
      } else {
        window.location.href = '/page1';
      }
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Check if there's a pre-filled title (e.g., from browser autofill)
      const jobTitleInput = document.getElementById('job-title');
      if (jobTitleInput.value.trim()) {
        console.log("Found pre-filled title:", jobTitleInput.value.trim());
      }

      // Check if we have a selected job_type from localStorage
      const selectedJobType = localStorage.getItem('selected_job_type');
      if (selectedJobType) {
        console.log("Found selected job_type in localStorage:", selectedJobType);

        // Create a form and submit it to save the job_type
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/save_page1';
        form.style.display = 'none';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'job_type';
        input.value = selectedJobType;
        form.appendChild(input);

        document.body.appendChild(form);
        form.submit();

        // Clear the localStorage
        localStorage.removeItem('selected_job_type');
      }

      // Initialize the Next button state
      updateNextButtonState();

      // Hide all specialty groups initially
      hideAllSpecialtyGroups();

      // Show the "other" specialty group by default
      showSpecialtyGroup('other');

      // Validate that all categories have specialties
      validateCategorySpecialties();

      // Add event listener for title input to ensure it updates the button state
      jobTitleInput.addEventListener('input', function() {
        handleTitleInput(this.value);
      });

      // Add event listener for the Next button to validate before submission
      document.querySelector('.next-button').addEventListener('click', function(e) {
        const jobTitle = document.getElementById('job-title').value.trim();

        if (!jobTitle || !selectedCategory) {
          e.preventDefault();
          updateNextButtonState(); // This will update the button state and disable it if needed
        }
      });

      console.log("Page initialized - Ready for user input");
    });

    // Function to validate that all categories have specialties
    function validateCategorySpecialties() {
      const categoryOptions = document.querySelectorAll('#categoryDropdown option:not([disabled])');
      const categoriesWithoutSpecialties = [];

      categoryOptions.forEach(option => {
        const categoryName = option.text;
        const matchingGroup = document.querySelector(`.specialty-group[data-category="${categoryName}"]`);

        if (!matchingGroup && categoryName !== 'Select a category') {
          console.warn(`Category "${categoryName}" has no specialties defined.`);
          categoriesWithoutSpecialties.push(categoryName);
        }
      });

      if (categoriesWithoutSpecialties.length > 0) {
        console.warn('Categories without specialties:', categoriesWithoutSpecialties);
      }
    }

    function animateElement(element, animation, duration = 600) {
      if (!element) return;

      // Add animation class
      element.classList.add(animation);

      // Remove animation class after duration
      setTimeout(() => {
        element.classList.remove(animation);
      }, duration);
    }

    function handleTitleInput(value) {
      // Store the title value
      const jobTitle = value.trim();

      // Update the Next button state based on all required fields
      updateNextButtonState();

      // Log for debugging
      console.log("Title input changed:", jobTitle, "Category:", selectedCategory, "Specialty:", selectedSpecialty);
    }

    function openCategoryModal() {
      document.getElementById('categoryModal').style.display = 'block';
      document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    function closeCategoryModal() {
      document.getElementById('categoryModal').style.display = 'none';
      document.body.style.overflow = 'auto'; // Re-enable scrolling
    }

    function toggleCategorySelectionType(type) {
      const predefinedCategory = document.getElementById('predefined-category');
      const customCategory = document.getElementById('custom-category');
      const toggleButtons = document.querySelectorAll('.selection-type-toggle:first-child button');

      // Update toggle buttons
      toggleButtons.forEach(button => {
        button.classList.remove('active');
      });

      // Find the clicked button and make it active
      const activeButton = document.querySelector(`.selection-toggle[onclick*="toggleCategorySelectionType('${type}')"]`);
      if (activeButton) {
        activeButton.classList.add('active');
      }

      if (type === 'predefined') {
        predefinedCategory.style.display = 'block';
        customCategory.style.display = 'none';
      } else {
        predefinedCategory.style.display = 'none';
        customCategory.style.display = 'block';
      }
    }

    function toggleSpecialtySelectionType(type) {
      const predefinedSpecialty = document.getElementById('predefined-specialty');
      const customSpecialty = document.getElementById('custom-specialty');
      const toggleButtons = document.querySelectorAll('.selection-type-toggle:nth-child(2) button');

      // Update toggle buttons
      toggleButtons.forEach(button => {
        button.classList.remove('active');
      });

      // Find the clicked button and make it active
      const activeButton = document.querySelector(`.selection-toggle[onclick*="toggleSpecialtySelectionType('${type}')"]`);
      if (activeButton) {
        activeButton.classList.add('active');
      }

      if (type === 'predefined') {
        predefinedSpecialty.style.display = 'block';
        customSpecialty.style.display = 'none';
      } else {
        predefinedSpecialty.style.display = 'none';
        customSpecialty.style.display = 'block';
      }
    }

    function hideAllSpecialtyGroups() {
      const specialtyGroups = document.querySelectorAll('.specialty-group');
      specialtyGroups.forEach(group => {
        group.style.display = 'none';
      });
    }

    function showSpecialtyGroup(category) {
      hideAllSpecialtyGroups();

      // Show the matching specialty group
      const matchingGroup = document.querySelector(`.specialty-group[data-category="${category}"]`);
      if (matchingGroup) {
        matchingGroup.style.display = 'block';

        // Select the "None" option by default
        const noneOption = matchingGroup.querySelector('option[value="none"]');
        if (noneOption) {
          noneOption.selected = true;
        } else {
          // Fallback to first option if no "None" option exists
          const firstOption = matchingGroup.querySelector('option');
          if (firstOption) {
            firstOption.selected = true;
          }
        }

        // Enable the Apply button since we have specialties for this category
        document.querySelector('.modal-buttons button:last-child').disabled = false;
      } else {
        // If no matching group, show the "other" group
        const otherGroup = document.querySelector(`.specialty-group[data-category="other"]`);
        if (otherGroup) {
          otherGroup.style.display = 'block';

          // Select the first option in the other group
          const firstOption = otherGroup.querySelector('option');
          if (firstOption) {
            firstOption.selected = true;
          }

          // Enable the Apply button since we have a fallback specialty
          document.querySelector('.modal-buttons button:last-child').disabled = false;
        } else {
          // No specialties available for this category, but that's okay - specialty is optional
          console.log(`No specialties found for category: ${category}, but specialty is optional so continuing.`);

          // Keep the Apply button enabled since specialty is optional
          document.querySelector('.modal-buttons button:last-child').disabled = false;
        }
      }
    }

    function updateSpecialties() {
      const categorySelect = document.getElementById('categoryDropdown');
      const selectedCategory = categorySelect.value;

      if (selectedCategory) {
        showSpecialtyGroup(selectedCategory);
      } else {
        hideAllSpecialtyGroups();
      }
    }

    function applyCategory() {
      // Check if using predefined or custom category
      const isPredefinedCategory = document.getElementById('predefined-category').style.display !== 'none';
      const isPredefinedSpecialty = document.getElementById('predefined-specialty').style.display !== 'none';

      let categoryValue, specialtyValue;

      // Get category value
      if (isPredefinedCategory) {
        const categorySelect = document.getElementById('categoryDropdown');
        if (!categorySelect.value) {
          alert('Please select a category');
          return;
        }
        categoryValue = categorySelect.options[categorySelect.selectedIndex].text;

        // Check if this category has specialties when using predefined categories
        if (isPredefinedSpecialty) {
          const matchingGroup = document.querySelector(`.specialty-group[data-category="${categoryValue}"]`);
          if (!matchingGroup && document.getElementById('predefined-specialty').style.display !== 'none') {
            // No specialties available for this category, but that's okay - specialty is optional
            console.log(`No specialties available for ${categoryValue}, but continuing anyway since specialty is optional.`);
          }
        }
      } else {
        const customCategoryInput = document.getElementById('custom-category-input');
        categoryValue = customCategoryInput.value.trim();

        if (!categoryValue) {
          alert('Please enter a custom category');
          return;
        }
      }

      // Get specialty value (optional)
      if (isPredefinedSpecialty) {
        const specialtySelect = document.getElementById('specialtyDropdown');
        // Check if there are any specialty options available for this category
        const hasSpecialtyOptions = specialtySelect.options.length > 0 &&
                                   Array.from(specialtySelect.options).some(option => option.style.display !== 'none');

        if (hasSpecialtyOptions && specialtySelect.selectedIndex > -1) {
          const selectedValue = specialtySelect.options[specialtySelect.selectedIndex].value;
          if (selectedValue === 'none') {
            specialtyValue = ''; // User explicitly chose "None"
          } else {
            specialtyValue = specialtySelect.options[specialtySelect.selectedIndex].text;
          }
        } else {
          specialtyValue = ''; // No specialty selected, but that's okay
        }
      } else {
        const customSpecialtyInput = document.getElementById('custom-specialty-input');
        specialtyValue = customSpecialtyInput.value.trim();
        // Custom specialty is also optional - if empty, that's fine
      }

      // Update the selected category and specialty
      selectedCategory = categoryValue;
      selectedSpecialty = specialtyValue;

      // Update the display
      document.getElementById('selected-category-text').textContent = selectedCategory;
      document.getElementById('selected-specialty-text').textContent =
        specialtyValue || 'None';

      // Add the selected class to the container
      document.getElementById('selected-category-container').classList.add('selected');

      // Update the Next button state
      updateNextButtonState();

      // Close the modal
      closeCategoryModal();
    }

    function updateNextButtonState() {
      const jobTitle = document.getElementById('job-title').value.trim();
      const nextButton = document.querySelector('.next-button');

      // Check if all required fields are filled
      const isTitleFilled = jobTitle.length > 0;
      const isCategoryFilled = selectedCategory && selectedCategory.length > 0;
      const isSpecialtyFilled = selectedSpecialty && selectedSpecialty.length > 0;

      // Specialty is always optional now
      const isSpecialtyRequired = false;

      // Determine if the form is valid - only title and category are required
      const isFormValid = isTitleFilled && isCategoryFilled;

      // Log the validation state for debugging
      console.log("Form validation:", {
        title: isTitleFilled,
        category: isCategoryFilled,
        specialty: isSpecialtyFilled,
        specialtyRequired: isSpecialtyRequired,
        formValid: isFormValid
      });

      // Update button state
      if (isFormValid) {
        nextButton.classList.add('active');
        nextButton.disabled = false;
        nextButton.style.opacity = '1';
        nextButton.style.cursor = 'pointer';
      } else {
        nextButton.classList.remove('active');
        nextButton.disabled = true;
        nextButton.style.opacity = '0.6';
        nextButton.style.cursor = 'not-allowed';
      }
    }

    function goToPage3() {
      const jobTitle = document.getElementById('job-title').value.trim();

      // Check if all required fields are filled
      if (!jobTitle) {
        alert("Please enter a job title.");
        document.getElementById('job-title').focus();
        return;
      }

      if (!selectedCategory) {
        alert("Please select a job category.");
        openCategoryModal();
        return;
      }

      // Create a form to submit the data
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/save_page2';

      // Add title input
      const titleInput = document.createElement('input');
      titleInput.type = 'hidden';
      titleInput.name = 'title';
      titleInput.value = jobTitle;
      form.appendChild(titleInput);

      // Add category input
      const categoryInput = document.createElement('input');
      categoryInput.type = 'hidden';
      categoryInput.name = 'job_category';
      categoryInput.value = selectedCategory;
      form.appendChild(categoryInput);

      // Add specialty input if available
      if (selectedSpecialty) {
        const specialtyInput = document.createElement('input');
        specialtyInput.type = 'hidden';
        specialtyInput.name = 'specialty';
        specialtyInput.value = selectedSpecialty;
        form.appendChild(specialtyInput);
      }

      // Log what we're submitting for debugging
      console.log("Submitting form to /save_page2 with:", {
        title: jobTitle,
        job_category: selectedCategory,
        specialty: selectedSpecialty || 'Not set'
      });

      // Append form to body and submit
      document.body.appendChild(form);
      form.submit();
    }
  </script>

  <style>
    /* Animation styles */
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    @keyframes fadeIn {
      0% { opacity: 0; transform: translateY(-10px); }
      100% { opacity: 1; transform: translateY(0); }
    }

    .pulse {
      animation: pulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Category styles */
    .category-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .required-badge {
      background-color: var(--primary-pink);
      color: white;
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 1rem;
      font-weight: 500;
    }

    #selected-category-container {
      background-color: var(--neutral-200);
      border: 1px solid var(--neutral-300);
      border-radius: var(--border-radius-md);
      padding: 1rem;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    #selected-category-container.selected {
      background-color: var(--primary-light-blue);
      border-color: var(--primary-blue);
    }

    #selected-category-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    #selected-category-text {
      font-weight: 600;
      color: var(--neutral-800);
      margin: 0;
    }

    .specialty-text {
      color: var(--neutral-600);
      font-size: 0.9rem;
      margin: 0;
    }

    /* Selection toggle styles */
    .selection-type-toggle {
      display: flex;
      margin-bottom: 1rem;
      border-radius: var(--border-radius-md);
      overflow: hidden;
      border: 1px solid var(--neutral-300);
    }

    .selection-toggle {
      flex: 1;
      padding: 0.75rem 1rem;
      background-color: var(--neutral-200);
      border: none;
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all 0.3s ease;
      text-align: center;
      color: var(--neutral-700);
    }

    .selection-toggle:first-child {
      border-right: 1px solid var(--neutral-300);
    }

    .selection-toggle.active {
      background-color: var(--primary-blue);
      color: white;
    }

    .selection-toggle:hover:not(.active) {
      background-color: var(--neutral-300);
    }

    /* Required field indicator */
    .required {
      color: var(--primary-pink);
    }

    /* Optional field indicator */
    .optional {
      color: #6b7280;
      font-weight: normal;
      font-size: 0.9em;
    }

    /* None option styling */
    option[value="none"] {
      font-style: italic;
      color: #6b7280;
    }

    /* Next button styles */
    .next-button {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .next-button.active {
      opacity: 1;
      cursor: pointer;
    }

    /* Modal button styles */
    .modal-buttons button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: var(--neutral-300);
      color: var(--neutral-600);
    }
  </style>
</body>
</html>
