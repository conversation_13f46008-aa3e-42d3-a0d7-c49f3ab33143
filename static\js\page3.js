// Store job details
let jobDetails = {
    title: document.getElementById('job-title').textContent,
    category: document.getElementById('job-category').textContent,
    specialty: document.getElementById('job-specialty').textContent,
    description: '',
    skills: [],
    scope: {
        description: '',
        size: '',
        duration: {
            value: 0,
            unit: ''
        },
        experienceLevel: ''
    },
    budget: {
        type: 'fixed',
        amount: 0,
        min: 0,
        max: 0
    },
    job_type: 'One-time project'  // Default job type
};

// Animation effects
const animateElement = (element, animation, duration = 300) => {
    element.style.animation = `${animation} ${duration}ms forwards`;
    return new Promise(resolve => {
        setTimeout(() => {
            element.style.animation = '';
            resolve();
        }, duration);
    });
};

// Global variable to prevent rapid modal opening
let modalOpening = false;

// Open modal with animation
function openEditModal(type) {
    console.log("openEditModal called with type:", type);

    // Prevent rapid double-clicks
    if (modalOpening) {
        console.log("Modal opening in progress, ignoring duplicate call");
        return;
    }

    modalOpening = true;

    const modalMap = {
        'title': 'titleModal',
        'category': 'categoryModal',
        'description': 'descriptionModal',
        'skills': 'skillsModal',
        'scope': 'scopeModal',
        'budget': 'budgetModal'
    };

    const modalId = modalMap[type];
    if (!modalId) {
        modalOpening = false;
        return;
    }

    const modal = document.getElementById(modalId);

    // Check if modal is already open to prevent double opening
    if (modal.style.display === 'flex') {
        console.log("Modal already open, preventing double open");
        modalOpening = false;
        return;
    }

    // Populate current values
    if (type === 'title') {
        document.getElementById('edit-title').value = jobDetails.title;
    } else if (type === 'category') {
        console.log("Opening category modal with current values:",
                   "Category:", jobDetails.category,
                   "Specialty:", jobDetails.specialty);

        // Set the category dropdown value
        const categorySelect = document.getElementById('edit-category');

        // Find the option with matching text content
        let categoryFound = false;
        for (let i = 0; i < categorySelect.options.length; i++) {
            if (categorySelect.options[i].textContent === jobDetails.category) {
                categorySelect.selectedIndex = i;
                categoryFound = true;
                console.log("Found matching category option at index:", i);
                break;
            }
        }

        // If category not found in dropdown, switch to custom input
        if (!categoryFound) {
            console.log("Category not found in dropdown, switching to custom input");
            toggleCategorySelectionType('custom');
            document.getElementById('custom-category-input').value = jobDetails.category;
        }

        // Initialize specialty dropdown based on current category
        updateSpecialties();

        // Set the specialty dropdown value
        const specialtySelect = document.getElementById('edit-specialty');
        if (jobDetails.specialty && jobDetails.specialty.trim() !== '') {
            // Check if the specialty exists in the dropdown
            let found = false;
            const options = specialtySelect.querySelectorAll('option');
            for (const option of options) {
                if (option.textContent === jobDetails.specialty) {
                    option.selected = true;
                    found = true;
                    console.log("Found matching specialty option:", option.textContent);
                    break;
                }
            }

            // If specialty not found in dropdown, switch to custom input
            if (!found && jobDetails.specialty !== 'General') {
                console.log("Specialty not found in dropdown, switching to custom input");
                toggleSpecialtySelectionType('custom');
                document.getElementById('custom-specialty-input').value = jobDetails.specialty;
            }
        } else {
            // No specialty or empty specialty - ensure we're using predefined and no option is selected
            console.log("No specialty set, ensuring predefined mode with no selection");
            toggleSpecialtySelectionType('predefined');
            // Clear any selection in specialty dropdown
            specialtySelect.selectedIndex = -1;
        }
    } else if (type === 'description') {
        const descriptionText = document.getElementById('job-description').value;
        document.getElementById('edit-description').value = descriptionText;
    } else if (type === 'skills') {
        // Clear the input field
        document.getElementById('skills-input').value = '';

        // Clear existing skills in the edit list
        const skillsList = document.getElementById('edit-skills-list');
        skillsList.innerHTML = '';

        // Add current skills to the edit list
        jobDetails.skills.forEach(skill => {
            addSkillToEditList(skill);
        });

        // Set up event listeners for the skills input
        const skillsInput = document.getElementById('skills-input');
        const addSkillBtn = document.getElementById('add-skill-btn');

        // Remove existing event listeners
        skillsInput.removeEventListener('keydown', handleSkillInputKeydown);
        addSkillBtn.removeEventListener('click', handleAddSkillBtnClick);

        // Add new event listeners
        skillsInput.addEventListener('keydown', handleSkillInputKeydown);
        addSkillBtn.addEventListener('click', handleAddSkillBtnClick);
    } else if (type === 'scope') {
        console.log("Opening scope modal with current values:", jobDetails.scope);

        // Set description
        document.getElementById('edit-scope').value = jobDetails.scope.description;

        // Set project size if it exists
        if (jobDetails.scope.size) {
            const sizeRadio = document.getElementById(`size-${jobDetails.scope.size}`);
            if (sizeRadio) {
                sizeRadio.checked = true;
                console.log(`Set project size radio to ${jobDetails.scope.size}`);
            } else {
                console.log(`Could not find size radio for ${jobDetails.scope.size}`);
            }
        } else {
            // Uncheck all size options
            const sizeOptions = document.getElementsByName('project-size');
            sizeOptions.forEach(option => {
                option.checked = false;
            });
            console.log("Unchecked all size options");
        }

        // Set duration
        document.getElementById('scope-duration-value').value = jobDetails.scope.duration.value;
        document.getElementById('scope-duration-unit').value = jobDetails.scope.duration.unit;
        console.log(`Set duration to ${jobDetails.scope.duration.value} ${jobDetails.scope.duration.unit}`);

        // Update duration options to show correct singular/plural forms
        if (typeof updateDurationOptions === 'function') {
            updateDurationOptions();
        }

        // Set experience level
        document.getElementById('scope-experience').value = jobDetails.scope.experienceLevel;
        console.log(`Set experience level to ${jobDetails.scope.experienceLevel}`);
    } else if (type === 'budget') {
        console.log("Opening budget modal with current values:", jobDetails.budget);

        // Set default budget type if not set
        const budgetType = jobDetails.budget.type || 'fixed';

        // Set budget type radio button
        let budgetTypeRadio;
        if (budgetType === 'hourly' || budgetType === 'time_tracking') {
            budgetTypeRadio = document.getElementById('budget-hourly');
        } else {
            budgetTypeRadio = document.getElementById('budget-fixed');
        }

        if (budgetTypeRadio) {
            budgetTypeRadio.checked = true;
            console.log(`Set budget type radio to ${budgetType}`);
        }

        // Toggle appropriate fields based on budget type
        const mainType = (budgetType === 'hourly' || budgetType === 'time_tracking') ? 'hourly' : 'fixed';
        toggleBudgetFields(mainType);

        // Set budget values based on type
        if (mainType === 'hourly') {
            document.getElementById('budget-hourly-rate').value = jobDetails.budget.amount || 0;

            // Set time limit value (always visible now)
            document.getElementById('weekly-time-limit').value = jobDetails.budget.weeklyTimeLimit || '';
        } else {
            // For fixed price, determine sub-type
            const subType = (budgetType === 'milestone') ? 'milestone' : 'one_time';

            // Set the fixed price sub-type radio
            if (subType === 'milestone') {
                document.getElementById('fixed-milestone').checked = true;
                // Set hidden field for milestone amount (will be calculated from individual milestones)
                document.getElementById('budget-milestone-amount').value = jobDetails.budget.amount || 0;

                // Set milestone count (handle custom values)
                const milestoneCount = jobDetails.budget.milestones || 3;
                const milestoneSelect = document.getElementById('milestone-count');

                if (milestoneCount > 10) {
                    // Use custom input for values > 10
                    milestoneSelect.value = 'custom';
                    document.getElementById('custom-milestone-count').value = milestoneCount;
                } else {
                    // Use dropdown for values <= 10
                    milestoneSelect.value = milestoneCount.toString();
                }

                // Generate milestone breakdown
                updateMilestoneBreakdown();

                // Load existing project title if available
                if (jobDetails.budget.projectTitle) {
                    const projectTitleInput = document.getElementById('project-main-title');
                    if (projectTitleInput) {
                        projectTitleInput.value = jobDetails.budget.projectTitle;
                    }
                }

                // Load existing milestone details if available
                if (jobDetails.budget.milestoneDetails) {
                    setTimeout(() => {
                        jobDetails.budget.milestoneDetails.forEach((milestone, index) => {
                            const titleInput = document.getElementById(`milestone-${index + 1}-title`);
                            const descriptionInput = document.getElementById(`milestone-${index + 1}-description`);
                            const deadlineInput = document.getElementById(`milestone-${index + 1}-deadline`);
                            const paymentInput = document.getElementById(`milestone-${index + 1}-payment`);

                            if (titleInput) titleInput.value = milestone.title || '';
                            if (descriptionInput) descriptionInput.value = milestone.description || '';
                            if (deadlineInput) deadlineInput.value = milestone.deadline || '';
                            if (paymentInput) paymentInput.value = milestone.payment || '';
                        });
                        updateMilestoneTotal();
                    }, 100);
                }
            } else {
                document.getElementById('fixed-one-time').checked = true;
                document.getElementById('budget-one-time-amount').value = jobDetails.budget.amount || 0;
            }

            // Toggle the sub-fields
            toggleFixedPriceFields(subType);
        }

        // Add event listeners only if not already added (prevent duplicates)
        if (!window.budgetEventListenersAdded) {
            // Add event listeners for main budget type radio buttons
            document.getElementById('budget-hourly').addEventListener('change', function() {
                if (this.checked) toggleBudgetFields('hourly');
            });

            document.getElementById('budget-fixed').addEventListener('change', function() {
                if (this.checked) {
                    toggleBudgetFields('fixed');
                    // Default to one-time pay when switching to fixed
                    document.getElementById('fixed-one-time').checked = true;
                    toggleFixedPriceFields('one_time');
                }
            });

            // Add event listeners for fixed price sub-type radio buttons
            document.getElementById('fixed-one-time').addEventListener('change', function() {
                if (this.checked) toggleFixedPriceFields('one_time');
            });

            document.getElementById('fixed-milestone').addEventListener('change', function() {
                if (this.checked) toggleFixedPriceFields('milestone');
            });

            // Mark that event listeners have been added
            window.budgetEventListenersAdded = true;
        }

        console.log("Opening budget modal with values:", jobDetails.budget);
    }

    // Show modal with animation
    modal.style.display = 'flex';
    const modalContent = modal.querySelector('.modal-content');
    animateElement(modalContent, 'modal-in', 400);

    // Reset the modalOpening flag after a short delay
    setTimeout(() => {
        modalOpening = false;
    }, 500);
}

// Close modal with animation
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    const modalContent = modal.querySelector('.modal-content');

    animateElement(modalContent, 'modal-out', 300).then(() => {
        modal.style.display = 'none';
        // Reset the modalOpening flag when modal is closed
        modalOpening = false;
    });
}

// Add keyframe animations
const style = document.createElement('style');
style.textContent = `
    @keyframes modal-in {
        from { opacity: 0; transform: scale(0.8) translateY(30px); }
        to { opacity: 1; transform: scale(1) translateY(0); }
    }

    @keyframes modal-out {
        from { opacity: 1; transform: scale(1) translateY(0); }
        to { opacity: 0; transform: scale(0.8) translateY(30px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Helper function to add a skill to the edit list
function addSkillToEditList(skill) {
    if (!skill || skill.trim() === '') return;

    // Check if skill already exists
    const existingSkills = Array.from(document.querySelectorAll('#edit-skills-list .skill-tag'))
        .map(tag => tag.dataset.skill);

    if (existingSkills.includes(skill.trim())) {
        // Highlight the existing skill briefly
        const existingTag = document.querySelector(`#edit-skills-list .skill-tag[data-skill="${skill.trim()}"]`);
        existingTag.style.animation = 'pulse 0.5s';
        setTimeout(() => {
            existingTag.style.animation = '';
        }, 500);
        return;
    }

    const skillsList = document.getElementById('edit-skills-list');
    const skillTag = document.createElement('span');
    skillTag.className = 'skill-tag';
    skillTag.textContent = skill.trim();
    skillTag.dataset.skill = skill.trim();
    skillTag.addEventListener('click', function() {
        this.remove();
    });

    skillsList.appendChild(skillTag);

    // Clear the input field
    document.getElementById('skills-input').value = '';
}

// Helper function to add a skill from suggestion
function addSkillFromSuggestion(skill) {
    addSkillToEditList(skill);

    // Add focus to the input field
    document.getElementById('skills-input').focus();
}

// Handle keydown event for skills input
function handleSkillInputKeydown(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const skill = event.target.value.trim();
        if (skill) {
            addSkillToEditList(skill);
        }
    }
}

// Handle click event for add skill button
function handleAddSkillBtnClick() {
    const skillsInput = document.getElementById('skills-input');
    const skill = skillsInput.value.trim();
    if (skill) {
        addSkillToEditList(skill);
    }
}

// Function to toggle budget fields based on payment type
function toggleBudgetFields(type) {
    const hourlyFields = document.getElementById('hourly-fields');
    const fixedFields = document.getElementById('fixed-fields');

    // Hide all fields first
    hourlyFields.style.display = 'none';
    fixedFields.style.display = 'none';

    // Show the appropriate fields based on type
    if (type === 'hourly') {
        hourlyFields.style.display = 'block';
    } else if (type === 'fixed') {
        fixedFields.style.display = 'block';
    }
}

// Function to toggle fixed price sub-fields
function toggleFixedPriceFields(type) {
    const oneTimeSubFields = document.getElementById('one-time-sub-fields');
    const milestoneSubFields = document.getElementById('milestone-sub-fields');

    // Hide all sub-fields first
    oneTimeSubFields.style.display = 'none';
    milestoneSubFields.style.display = 'none';

    // Show the appropriate sub-fields based on type
    if (type === 'one_time') {
        oneTimeSubFields.style.display = 'block';
    } else if (type === 'milestone') {
        milestoneSubFields.style.display = 'block';
    }
}

// Function to update milestone breakdown
function updateMilestoneBreakdown() {
    const milestoneSelect = document.getElementById('milestone-count');
    const customMilestoneInput = document.getElementById('custom-milestone-input');
    const milestoneBreakdown = document.getElementById('milestone-breakdown');
    const milestoneContainer = document.getElementById('milestone-details-container');

    let milestoneCount;

    if (milestoneSelect.value === 'custom') {
        customMilestoneInput.style.display = 'block';
        milestoneCount = parseInt(document.getElementById('custom-milestone-count').value) || 0;

        if (milestoneCount === 0) {
            milestoneBreakdown.style.display = 'none';
            return;
        }
    } else {
        customMilestoneInput.style.display = 'none';
        document.getElementById('custom-milestone-count').value = '';
        milestoneCount = parseInt(milestoneSelect.value) || 3;
    }

    // Show milestone breakdown section
    milestoneBreakdown.style.display = 'block';

    // Clear existing milestone details
    milestoneContainer.innerHTML = '';

    // Generate milestone detail fields
    for (let i = 1; i <= milestoneCount; i++) {
        const milestoneItem = document.createElement('div');
        milestoneItem.className = 'milestone-item';
        milestoneItem.innerHTML = `
            <div class="milestone-header">
                <div class="milestone-number">${i}</div>
                <span>Milestone ${i}</span>
            </div>
            <div class="milestone-fields">
                <div class="form-group">
                    <label for="milestone-${i}-title">Activity/Title <span style="color: #e74c3c;">*</span></label>
                    <input type="text" id="milestone-${i}-title" class="form-control" placeholder="e.g., Design Phase, Development, Testing" required>
                </div>
                <div class="form-group">
                    <label for="milestone-${i}-description">Description <span style="color: #6c757d; font-weight: normal;">(Optional)</span></label>
                    <textarea id="milestone-${i}-description" class="form-control" rows="2" placeholder="Optional: Add more details about this milestone..."></textarea>
                </div>
                <div class="milestone-bottom-row">
                    <div class="form-group">
                        <label for="milestone-${i}-deadline">Deadline <span style="color: #e74c3c;">*</span></label>
                        <input type="date" id="milestone-${i}-deadline" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="milestone-${i}-payment">Payment ($) <span style="color: #e74c3c;">*</span></label>
                        <input type="number" id="milestone-${i}-payment" class="form-control" min="0" step="0.01" placeholder="0.00" onchange="updateMilestoneTotal()" required>
                    </div>
                </div>
            </div>
        `;
        milestoneContainer.appendChild(milestoneItem);
    }

    // Update total
    updateMilestoneTotal();
}

// Function to update milestone total
function updateMilestoneTotal() {
    const milestoneContainer = document.getElementById('milestone-details-container');
    const totalDisplay = document.getElementById('milestone-total-display');

    if (!milestoneContainer || !totalDisplay) return;

    let total = 0;
    const paymentInputs = milestoneContainer.querySelectorAll('input[id*="-payment"]');

    paymentInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        total += value;
    });

    totalDisplay.textContent = total.toFixed(2);

    // Update the main budget field to match the total
    const mainBudgetField = document.getElementById('budget-milestone-amount');
    if (mainBudgetField && total > 0) {
        mainBudgetField.value = total.toFixed(2);
    }
}



// Function to toggle between predefined and custom category
function toggleCategorySelectionType(type) {
    const predefinedCategory = document.getElementById('predefined-category');
    const customCategory = document.getElementById('custom-category');
    const toggleButtons = document.querySelectorAll('.selection-type-toggle button');

    // Update toggle buttons
    toggleButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Find the clicked button and make it active
    const activeButton = document.querySelector(`.selection-toggle[onclick*="${type}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    if (type === 'predefined') {
        predefinedCategory.style.display = 'block';
        customCategory.style.display = 'none';
    } else {
        predefinedCategory.style.display = 'none';
        customCategory.style.display = 'block';
    }
}

// Function to toggle between predefined and custom specialty
function toggleSpecialtySelectionType(type) {
    const predefinedSpecialty = document.getElementById('predefined-specialty');
    const customSpecialty = document.getElementById('custom-specialty');
    const toggleButtons = document.querySelectorAll('.selection-type-toggle:nth-child(2) button');

    // Update toggle buttons
    toggleButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Find the clicked button and make it active
    const activeButton = document.querySelector(`.selection-toggle[onclick*="${type}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    if (type === 'predefined') {
        predefinedSpecialty.style.display = 'block';
        customSpecialty.style.display = 'none';
    } else {
        predefinedSpecialty.style.display = 'none';
        customSpecialty.style.display = 'block';
    }
}

// Update duration dropdown options based on entered value
function updateDurationOptions() {
    const durationValueInput = document.getElementById('scope-duration-value');
    const durationUnit = document.getElementById('scope-duration-unit');

    if (!durationValueInput || !durationUnit) {
        return; // Elements not found, exit gracefully
    }

    const durationValue = parseInt(durationValueInput.value);
    const currentValue = durationUnit.value;

    // Clear existing options
    durationUnit.innerHTML = '';

    // Determine if we should use singular or plural forms
    const isSingular = durationValue === 1;

    // Add options with appropriate singular/plural forms
    const daysOption = document.createElement('option');
    daysOption.value = 'days';
    daysOption.textContent = isSingular ? 'Day' : 'Days';
    durationUnit.appendChild(daysOption);

    const weeksOption = document.createElement('option');
    weeksOption.value = 'weeks';
    weeksOption.textContent = isSingular ? 'Week' : 'Weeks';
    durationUnit.appendChild(weeksOption);

    const monthsOption = document.createElement('option');
    monthsOption.value = 'months';
    monthsOption.textContent = isSingular ? 'Month' : 'Months';
    durationUnit.appendChild(monthsOption);

    // Restore the previously selected value or set default
    if (currentValue) {
        durationUnit.value = currentValue;
    } else {
        // Auto-suggest unit based on typical project durations
        if (durationValue > 0) {
            if (durationValue <= 14) {
                durationUnit.value = 'days';
            } else if (durationValue <= 12) {
                durationUnit.value = 'weeks';
            } else {
                durationUnit.value = 'months';
            }
        } else {
            durationUnit.value = 'days'; // Default
        }
    }
}

// Function to update specialties based on selected category
function updateSpecialties() {
    const categorySelect = document.getElementById('edit-category');
    const selectedCategory = categorySelect.options[categorySelect.selectedIndex].textContent;

    console.log("Updating specialties for category:", selectedCategory);

    // Hide all specialty groups first
    const specialtyGroups = document.querySelectorAll('.specialty-group');
    specialtyGroups.forEach(group => {
        group.style.display = 'none';
    });

    // Show only the specialty group that matches the selected category
    const matchingGroup = document.querySelector(`.specialty-group[data-category="${selectedCategory}"]`);
    if (matchingGroup) {
        console.log("Found matching specialty group:", matchingGroup.getAttribute('label'));
        matchingGroup.style.display = 'block';

        // Select the first option in the matching group
        const firstOption = matchingGroup.querySelector('option');
        if (firstOption) {
            firstOption.selected = true;
            console.log("Selected first specialty option:", firstOption.textContent);
        }
    } else {
        console.log("No matching specialty group found for category:", selectedCategory);
    }
}

// Removed scope templates function

// Save edits with validation
function saveEdit(type) {
    if (type === 'title') {
        const newTitle = document.getElementById('edit-title').value.trim();

        // Clear any previous error styling
        document.getElementById('edit-title').classList.remove('validation-error');

        if (!newTitle) {
            document.getElementById('edit-title').classList.add('validation-error');
            document.getElementById('edit-title').focus();
            showToast('Please enter a job title', 'error');
            return;
        }

        jobDetails.title = newTitle;
        document.getElementById('job-title').textContent = newTitle;
        closeModal('titleModal');
        showToast('Job title updated successfully');

    } else if (type === 'category') {
        // Clear any previous error styling
        document.querySelectorAll('#categoryModal .form-control').forEach(el => {
            el.classList.remove('validation-error');
        });

        // Check if using predefined or custom category
        const isPredefinedCategory = document.getElementById('predefined-category').style.display !== 'none';
        const isPredefinedSpecialty = document.getElementById('predefined-specialty').style.display !== 'none';

        let categoryValue, specialtyValue;

        // Get category value
        if (isPredefinedCategory) {
            const categorySelect = document.getElementById('edit-category');
            // Use textContent instead of value to get the display text
            categoryValue = categorySelect.options[categorySelect.selectedIndex].textContent;
            console.log("Selected category:", categoryValue);
        } else {
            const customCategoryInput = document.getElementById('custom-category-input');
            categoryValue = customCategoryInput.value.trim();

            if (!categoryValue) {
                customCategoryInput.classList.add('validation-error');
                customCategoryInput.focus();
                showToast('Please enter a custom category', 'error');
                return;
            }
        }

        // Get specialty value (specialty is optional)
        if (isPredefinedSpecialty) {
            const specialtySelect = document.getElementById('edit-specialty');
            if (specialtySelect.selectedIndex >= 0) {
                // Use textContent instead of value to get the display text
                specialtyValue = specialtySelect.options[specialtySelect.selectedIndex].textContent;
                console.log("Selected specialty:", specialtyValue);
            } else {
                // No specialty selected, which is fine
                specialtyValue = '';
                console.log("No specialty selected");
            }
        } else {
            const customSpecialtyInput = document.getElementById('custom-specialty-input');
            specialtyValue = customSpecialtyInput.value.trim();
            // Custom specialty is optional - empty is allowed
            console.log("Custom specialty:", specialtyValue || 'None');
        }

        // Update job details
        jobDetails.category = categoryValue;
        jobDetails.specialty = specialtyValue;

        console.log("Updating job details - Category:", categoryValue, "Specialty:", specialtyValue);

        // Update display
        document.getElementById('job-category').textContent = jobDetails.category;
        document.getElementById('job-specialty').textContent = jobDetails.specialty || 'None';

        console.log("Updated display - Category:", document.getElementById('job-category').textContent,
                   "Specialty:", document.getElementById('job-specialty').textContent);

        closeModal('categoryModal');
        showToast('Job category and specialty updated successfully');

    } else if (type === 'description') {
        const newDescription = document.getElementById('edit-description').value.trim();

        // Clear any previous error styling
        document.getElementById('edit-description').classList.remove('validation-error');

        if (!newDescription) {
            document.getElementById('edit-description').classList.add('validation-error');
            document.getElementById('edit-description').focus();
            showToast('Please enter a job description', 'error');
            return;
        }

        jobDetails.description = newDescription;
        document.getElementById('job-description').value = newDescription;
        closeModal('descriptionModal');
        showToast('Job description updated successfully');

    } else if (type === 'skills') {
        // Get all skills from the edit list
        const skillTags = document.querySelectorAll('#edit-skills-list .skill-tag');
        const updatedSkills = Array.from(skillTags).map(tag => tag.dataset.skill);

        // Clear any previous error styling
        document.getElementById('edit-skills-list').classList.remove('validation-error');
        document.getElementById('skills-input').classList.remove('validation-error');

        if (updatedSkills.length === 0) {
            document.getElementById('edit-skills-list').classList.add('validation-error');
            document.getElementById('skills-input').classList.add('validation-error');
            document.getElementById('skills-input').focus();
            showToast('Please add at least one skill', 'error');
            return;
        }

        // Update job details
        jobDetails.skills = updatedSkills;

        // Update the skills display in the main view
        const skillsContainer = document.querySelector('.skills-list');
        skillsContainer.innerHTML = '';
        updatedSkills.forEach(skill => {
            const skillTag = document.createElement('span');
            skillTag.className = 'skill-tag';
            skillTag.textContent = skill;
            skillsContainer.appendChild(skillTag);
        });

        closeModal('skillsModal');
        showToast('Skills updated successfully');

    } else if (type === 'scope') {
        // Get description
        const newDescription = document.getElementById('edit-scope').value.trim();

        // Get project size
        const sizeOptions = document.getElementsByName('project-size');
        let selectedSize = ''; // Default is empty
        for (const option of sizeOptions) {
            if (option.checked) {
                selectedSize = option.value;
                break;
            }
        }

        // Get duration
        const durationValue = parseInt(document.getElementById('scope-duration-value').value) || 0;
        const durationUnit = document.getElementById('scope-duration-unit').value;

        // Get experience level
        const experienceLevel = document.getElementById('scope-experience').value;

        // Validation: Check for required fields
        const validationErrors = [];
        const invalidFields = [];

        // Clear any previous error styling
        document.querySelectorAll('.form-control, .scope-size-option').forEach(el => {
            el.classList.remove('validation-error');
        });

        if (!newDescription) {
            validationErrors.push('Project description is required');
            document.getElementById('edit-scope').classList.add('validation-error');
            invalidFields.push('description');
        }

        if (!selectedSize) {
            validationErrors.push('Project size must be selected');
            document.querySelectorAll('.scope-size-option').forEach(option => {
                option.classList.add('validation-error');
            });
            invalidFields.push('size');
        }

        if (durationValue <= 0) {
            validationErrors.push('Duration must be greater than 0');
            document.getElementById('scope-duration-value').classList.add('validation-error');
            invalidFields.push('duration');
        }

        if (!durationUnit) {
            validationErrors.push('Duration unit must be selected');
            document.getElementById('scope-duration-unit').classList.add('validation-error');
            invalidFields.push('duration unit');
        }

        if (!experienceLevel || experienceLevel === '') {
            validationErrors.push('Experience level must be selected');
            document.getElementById('scope-experience').classList.add('validation-error');
            invalidFields.push('experience level');
        }

        // If there are validation errors, show them and return
        if (validationErrors.length > 0) {
            const errorMessage = `Please fill in the following required fields: ${invalidFields.join(', ')}`;
            showToast(errorMessage, 'error');

            // Focus on the first invalid field
            const firstInvalidElement = document.querySelector('.validation-error');
            if (firstInvalidElement) {
                firstInvalidElement.focus();
            }

            return;
        }

        // Update job details
        jobDetails.scope = {
            description: newDescription,
            size: selectedSize,
            duration: {
                value: durationValue,
                unit: durationUnit
            },
            experienceLevel: experienceLevel
        };

        console.log("Updated job scope details:", jobDetails.scope);

        // Update the display
        document.getElementById('job-scope-description').textContent = newDescription;

        // Update size display with proper capitalization
        const sizeDisplay = selectedSize ? (selectedSize.charAt(0).toUpperCase() + selectedSize.slice(1)) : '0';
        document.getElementById('job-scope-size').textContent = sizeDisplay;

        // Update duration display
        let durationDisplay = '0';
        if (durationValue > 0 && durationUnit) {
            const unitDisplay = durationValue === 1 ? durationUnit.slice(0, -1) : durationUnit;
            durationDisplay = `${durationValue} ${unitDisplay}`;
        }
        document.getElementById('job-scope-duration').textContent = durationDisplay;

        // Update experience level display with proper capitalization
        let experienceDisplay = '0';
        if (experienceLevel) {
            switch(experienceLevel) {
                case 'entry':
                    experienceDisplay = 'Entry Level';
                    break;
                case 'intermediate':
                    experienceDisplay = 'Intermediate';
                    break;
                case 'expert':
                    experienceDisplay = 'Expert';
                    break;
                default:
                    experienceDisplay = experienceLevel;
            }
        }
        document.getElementById('job-scope-experience').textContent = experienceDisplay;

        closeModal('scopeModal');
        showToast('Project scope updated successfully');

    } else if (type === 'budget') {
        // Clear any previous error styling
        document.querySelectorAll('#budgetModal .form-control').forEach(el => {
            el.classList.remove('validation-error');
        });
        document.querySelectorAll('#budgetModal input[type="checkbox"]').forEach(el => {
            el.classList.remove('validation-error');
        });

        // Get main budget type
        const budgetTypeOptions = document.getElementsByName('budget-type');
        let selectedBudgetType = 'fixed'; // Default
        for (const option of budgetTypeOptions) {
            if (option.checked) {
                selectedBudgetType = option.value;
                break;
            }
        }

        let budgetDisplay = '';
        let updatedBudget = { type: selectedBudgetType };

        if (selectedBudgetType === 'hourly') {
            // Get hourly rate input (Time Tracking)
            const hourlyRateInput = document.getElementById('budget-hourly-rate');
            const hourlyRateValue = hourlyRateInput.value.trim();
            const hourlyRate = parseFloat(hourlyRateValue) || 0;

            // Validate hourly rate - check if empty first
            if (!hourlyRateValue) {
                hourlyRateInput.classList.add('validation-error');
                hourlyRateInput.focus();
                showToast('Please fill up the hourly rate field', 'error');
                return;
            }

            if (hourlyRate <= 0) {
                hourlyRateInput.classList.add('validation-error');
                hourlyRateInput.focus();
                showToast('Please enter a valid hourly rate greater than $0', 'error');
                return;
            }

            // Get weekly time limit (always required now)
            const weeklyTimeLimitInput = document.getElementById('weekly-time-limit');
            const weeklyTimeLimitValue = weeklyTimeLimitInput.value.trim();
            const weeklyTimeLimit = parseInt(weeklyTimeLimitValue) || 0;

            // Validate time limit - check if empty first
            if (!weeklyTimeLimitValue) {
                weeklyTimeLimitInput.classList.add('validation-error');
                weeklyTimeLimitInput.focus();
                showToast('Please fill up the weekly time limit field', 'error');
                return;
            }

            if (weeklyTimeLimit < 1 || weeklyTimeLimit > 168) {
                weeklyTimeLimitInput.classList.add('validation-error');
                weeklyTimeLimitInput.focus();
                showToast('Please enter a valid weekly time limit between 1 and 168 hours', 'error');
                return;
            }

            updatedBudget.subtype = 'hourly';  // Add subtype for hourly
            updatedBudget.amount = hourlyRate;
            updatedBudget.weeklyTimeLimit = weeklyTimeLimit;
            budgetDisplay = `$${hourlyRate}/hour (Max ${weeklyTimeLimit}h/week)`;

        } else if (selectedBudgetType === 'fixed') {
            // Get fixed price sub-type
            const fixedPriceOptions = document.getElementsByName('fixed-price-type');
            let selectedFixedType = 'one_time'; // Default
            for (const option of fixedPriceOptions) {
                if (option.checked) {
                    selectedFixedType = option.value;
                    break;
                }
            }

            if (selectedFixedType === 'milestone') {
                // Validate project main title first
                const projectTitleInput = document.getElementById('project-main-title');
                const projectTitle = projectTitleInput ? projectTitleInput.value.trim() : '';

                if (!projectTitle) {
                    if (projectTitleInput) {
                        projectTitleInput.classList.add('validation-error');
                        projectTitleInput.focus();
                    }
                    showToast('Please enter a project title', 'error');
                    return;
                }

                // Get milestone budget from hidden field (calculated from individual milestones)
                const milestoneAmount = parseFloat(document.getElementById('budget-milestone-amount').value) || 0;

                // Get milestone count (handle custom input)
                let milestoneCount;
                const milestoneSelect = document.getElementById('milestone-count');
                if (milestoneSelect.value === 'custom') {
                    const customMilestoneInput = document.getElementById('custom-milestone-count');
                    const customMilestoneValue = customMilestoneInput.value.trim();
                    milestoneCount = parseInt(customMilestoneValue) || 0;

                    // Validate custom milestone count - check if empty first
                    if (!customMilestoneValue) {
                        customMilestoneInput.classList.add('validation-error');
                        customMilestoneInput.focus();
                        showToast('Please fill up the custom milestone count field', 'error');
                        return;
                    }

                    if (milestoneCount < 11 || milestoneCount > 50) {
                        customMilestoneInput.classList.add('validation-error');
                        customMilestoneInput.focus();
                        showToast('Please enter a valid number of milestones between 11 and 50', 'error');
                        return;
                    }
                } else {
                    milestoneCount = parseInt(milestoneSelect.value) || 3;
                }

                // Validate milestone details
                const milestoneDetails = [];
                let hasEmptyMilestoneFields = false;

                for (let i = 1; i <= milestoneCount; i++) {
                    const titleInput = document.getElementById(`milestone-${i}-title`);
                    const descriptionInput = document.getElementById(`milestone-${i}-description`);
                    const deadlineInput = document.getElementById(`milestone-${i}-deadline`);
                    const paymentInput = document.getElementById(`milestone-${i}-payment`);

                    const title = titleInput ? titleInput.value.trim() : '';
                    const description = descriptionInput ? descriptionInput.value.trim() : '';
                    const deadline = deadlineInput ? deadlineInput.value : '';
                    const payment = paymentInput ? parseFloat(paymentInput.value) || 0 : 0;

                    // Validate each milestone field
                    if (!title) {
                        if (titleInput) {
                            titleInput.classList.add('validation-error');
                            titleInput.focus();
                        }
                        showToast(`Please fill up the title for Milestone ${i}`, 'error');
                        return;
                    }

                    if (!deadline) {
                        if (deadlineInput) {
                            deadlineInput.classList.add('validation-error');
                            deadlineInput.focus();
                        }
                        showToast(`Please set a deadline for Milestone ${i}`, 'error');
                        return;
                    }

                    if (payment <= 0) {
                        if (paymentInput) {
                            paymentInput.classList.add('validation-error');
                            paymentInput.focus();
                        }
                        showToast(`Please enter a valid payment amount for Milestone ${i}`, 'error');
                        return;
                    }

                    milestoneDetails.push({
                        title: title,
                        description: description, // Optional description
                        deadline: deadline,
                        payment: payment
                    });
                }

                // Calculate total from milestone payments
                const calculatedTotal = milestoneDetails.reduce((sum, milestone) => sum + milestone.payment, 0);

                // Validate that total is greater than 0
                if (calculatedTotal <= 0) {
                    showToast('Total milestone payments must be greater than $0', 'error');
                    return;
                }

                updatedBudget.type = 'fixed';  // Keep budget_type as 'fixed'
                updatedBudget.subtype = 'milestone';  // Add subtype for milestone
                updatedBudget.amount = calculatedTotal;
                updatedBudget.milestones = milestoneCount;
                updatedBudget.projectTitle = projectTitle;  // Add project title
                updatedBudget.milestoneDetails = milestoneDetails;
                budgetDisplay = `$${calculatedTotal} (${milestoneCount} Milestones)`;

            } else {
                // Get one time pay input
                const oneTimeAmountInput = document.getElementById('budget-one-time-amount');
                const oneTimeAmountValue = oneTimeAmountInput.value.trim();
                const oneTimeAmount = parseFloat(oneTimeAmountValue) || 0;

                // Validate one time pay amount - check if empty first
                if (!oneTimeAmountValue) {
                    oneTimeAmountInput.classList.add('validation-error');
                    oneTimeAmountInput.focus();
                    showToast('Please fill up the project amount field', 'error');
                    return;
                }

                if (oneTimeAmount <= 0) {
                    oneTimeAmountInput.classList.add('validation-error');
                    oneTimeAmountInput.focus();
                    showToast('Please enter a valid project amount greater than $0', 'error');
                    return;
                }

                updatedBudget.type = 'fixed';  // Keep budget_type as 'fixed'
                updatedBudget.subtype = 'one_time';  // Add subtype for one-time pay
                updatedBudget.amount = oneTimeAmount;
                budgetDisplay = `$${oneTimeAmount} (One time Pay)`;
            }
        }

        // Update job details
        jobDetails.budget = updatedBudget;
        console.log("Updated job budget details:", jobDetails.budget);

        // Update display
        document.getElementById('job-budget').textContent = budgetDisplay;
        closeModal('budgetModal');
        showToast('Budget updated successfully');

    }

    // Update job status
    updateJobStatus();
}

// Toast notification system
function showToast(message, type = 'success') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => {
        document.body.removeChild(toast);
    });

    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
        </div>
        <div class="toast-message">${message}</div>
    `;

    // Add toast styles if not already added
    if (!document.getElementById('toast-styles')) {
        const toastStyles = document.createElement('style');
        toastStyles.id = 'toast-styles';
        toastStyles.textContent = `
            .toast-notification {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                padding: 12px 20px;
                display: flex;
                align-items: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 9999;
                animation: toast-in 0.3s forwards, toast-out 0.3s forwards 3s;
                max-width: 300px;
            }

            .toast-success {
                border-left: 4px solid #4CAF50;
            }

            .toast-error {
                border-left: 4px solid #F44336;
            }

            .toast-icon {
                margin-right: 12px;
                font-size: 20px;
            }

            .toast-success .toast-icon {
                color: #4CAF50;
            }

            .toast-error .toast-icon {
                color: #F44336;
            }

            .toast-message {
                font-size: 14px;
                color: #333;
            }

            @keyframes toast-in {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes toast-out {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(toastStyles);
    }

    document.body.appendChild(toast);

    // Remove toast after animation
    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3300);
}

// Update job status based on completeness
function updateJobStatus() {
    const description = document.getElementById('job-description').value.trim();

    const requiredFields = [
        jobDetails.title,
        jobDetails.category,
        description,
        jobDetails.skills && jobDetails.skills.length > 0 ? 'skills' : '',
        jobDetails.scope && jobDetails.scope.description ? 'scope_desc' : '',
        jobDetails.scope && jobDetails.scope.size ? 'scope_size' : '',
        jobDetails.scope && jobDetails.scope.duration && jobDetails.scope.duration.value > 0 ? 'scope_duration' : '',
        jobDetails.scope && jobDetails.scope.experienceLevel ? 'scope_exp' : '',
        jobDetails.budget && jobDetails.budget.amount > 0 ? 'budget' : ''
    ];

    const completedFields = requiredFields.filter(field => field && field.toString().trim() !== '').length;
    const totalFields = requiredFields.length;
    const completionPercentage = Math.floor((completedFields / totalFields) * 100);

    const statusElement = document.querySelector('.job-status');

    if (completionPercentage === 100) {
        statusElement.textContent = 'Ready to Post';
        statusElement.className = 'job-status ready';
    } else if (completionPercentage >= 70) {
        statusElement.textContent = 'Almost Ready';
        statusElement.className = 'job-status progress';
    } else if (completionPercentage >= 40) {
        statusElement.textContent = 'In Progress';
        statusElement.className = 'job-status progress';
    } else {
        statusElement.textContent = 'Draft';
        statusElement.className = 'job-status draft';
    }

    // Pulse animation on status change
    animateElement(statusElement, 'pulse', 600);
}

// Highlight missing fields visually
function highlightMissingFields(missingFields) {
    // Clear previous highlights
    clearValidationErrors();

    missingFields.forEach(field => {
        let element;

        switch (field) {
            case 'Job Title':
                element = document.querySelector('#job-title').closest('.detail-section');
                break;
            case 'Job Category':
                element = document.querySelector('#job-category').closest('.detail-section');
                break;
            case 'Job Description':
                element = document.querySelector('#job-description').closest('.detail-section');
                break;
            case 'Required Skills':
                element = document.querySelector('#skills-container').closest('.detail-section');
                break;
            case 'Project Scope':
                element = document.querySelector('.scope-details').closest('.detail-section');
                break;
            case 'Budget':
            case 'Weekly Time Limit':
            case 'Milestone Details':
                element = document.querySelector('.budget-details').closest('.detail-section');
                break;
        }

        if (element) {
            element.classList.add('validation-error');
            // Remove the error class after 5 seconds
            setTimeout(() => {
                element.classList.remove('validation-error');
            }, 5000);
        }
    });
}

// Scroll to the first missing field
function scrollToFirstMissingField(missingFields) {
    if (missingFields.length === 0) return;

    const firstField = missingFields[0];
    let targetElement;

    switch (firstField) {
        case 'Job Title':
            targetElement = document.querySelector('#job-title').closest('.detail-section');
            break;
        case 'Job Category':
            targetElement = document.querySelector('#job-category').closest('.detail-section');
            break;
        case 'Job Description':
            targetElement = document.querySelector('#job-description').closest('.detail-section');
            break;
        case 'Required Skills':
            targetElement = document.querySelector('#skills-container').closest('.detail-section');
            break;
        case 'Project Scope':
            targetElement = document.querySelector('.scope-details').closest('.detail-section');
            break;
        case 'Budget':
        case 'Weekly Time Limit':
        case 'Milestone Details':
            targetElement = document.querySelector('.budget-details').closest('.detail-section');
            break;
    }

    if (targetElement) {
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Flash the section to draw attention
        setTimeout(() => {
            targetElement.style.transform = 'scale(1.02)';
            setTimeout(() => {
                targetElement.style.transform = 'scale(1)';
                targetElement.style.transition = 'transform 0.3s ease';
            }, 200);
        }, 500);
    }
}

// Clear all validation error highlights
function clearValidationErrors() {
    const errorElements = document.querySelectorAll('.validation-error');
    errorElements.forEach(element => {
        element.classList.remove('validation-error');
    });
}

// Submit job with validation
function submitJob(status = 'publish') {
    // Get all job details
    const description = document.getElementById('job-description').value.trim();
    jobDetails.description = description;

    // Comprehensive validation for publishing
    if (status === 'publish') {
        const validationErrors = [];
        const missingFields = [];

        // 1. Validate Job Title
        if (!jobDetails.title || jobDetails.title.trim() === '') {
            validationErrors.push('Job title is required');
            missingFields.push('Job Title');
        }

        // 2. Validate Category
        if (!jobDetails.category || jobDetails.category.trim() === '') {
            validationErrors.push('Job category is required');
            missingFields.push('Job Category');
        }

        // 3. Validate Description
        if (!description || description.trim() === '') {
            validationErrors.push('Job description is required');
            missingFields.push('Job Description');
        }

        // 4. Validate Skills
        if (!jobDetails.skills || jobDetails.skills.length === 0) {
            validationErrors.push('At least one skill is required');
            missingFields.push('Required Skills');
        }

        // 5. Validate Project Scope
        if (!jobDetails.scope ||
            !jobDetails.scope.description || jobDetails.scope.description.trim() === '' ||
            !jobDetails.scope.size || jobDetails.scope.size === '' ||
            !jobDetails.scope.duration || !jobDetails.scope.duration.value || jobDetails.scope.duration.value <= 0 ||
            !jobDetails.scope.duration.unit || jobDetails.scope.duration.unit === '' ||
            !jobDetails.scope.experienceLevel || jobDetails.scope.experienceLevel === '') {

            const scopeIssues = [];
            if (!jobDetails.scope || !jobDetails.scope.description || jobDetails.scope.description.trim() === '') {
                scopeIssues.push('project description');
            }
            if (!jobDetails.scope || !jobDetails.scope.size || jobDetails.scope.size === '') {
                scopeIssues.push('project size');
            }
            if (!jobDetails.scope || !jobDetails.scope.duration || !jobDetails.scope.duration.value || jobDetails.scope.duration.value <= 0) {
                scopeIssues.push('project duration');
            }
            if (!jobDetails.scope || !jobDetails.scope.experienceLevel || jobDetails.scope.experienceLevel === '') {
                scopeIssues.push('experience level');
            }

            validationErrors.push(`Project scope is incomplete: ${scopeIssues.join(', ')}`);
            missingFields.push('Project Scope');
        }

        // 6. Validate Budget
        if (!jobDetails.budget || !jobDetails.budget.amount || jobDetails.budget.amount <= 0) {
            validationErrors.push('Budget amount is required and must be greater than $0');
            missingFields.push('Budget');
        } else {
            // Additional budget validation based on type
            if (jobDetails.budget.type === 'hourly' && (!jobDetails.budget.weeklyTimeLimit || jobDetails.budget.weeklyTimeLimit <= 0)) {
                validationErrors.push('Weekly time limit is required for hourly jobs');
                missingFields.push('Weekly Time Limit');
            }

            if (jobDetails.budget.subtype === 'milestone') {
                if (!jobDetails.budget.milestoneDetails || jobDetails.budget.milestoneDetails.length === 0) {
                    validationErrors.push('Milestone details are required for milestone-based projects');
                    missingFields.push('Milestone Details');
                } else {
                    // Validate each milestone
                    for (let i = 0; i < jobDetails.budget.milestoneDetails.length; i++) {
                        const milestone = jobDetails.budget.milestoneDetails[i];
                        if (!milestone.title || milestone.title.trim() === '') {
                            validationErrors.push(`Milestone ${i + 1} title is required`);
                            break;
                        }
                        if (!milestone.deadline || milestone.deadline === '') {
                            validationErrors.push(`Milestone ${i + 1} deadline is required`);
                            break;
                        }
                        if (!milestone.payment || milestone.payment <= 0) {
                            validationErrors.push(`Milestone ${i + 1} payment must be greater than $0`);
                            break;
                        }
                    }
                }
            }
        }

        // Show validation errors if any
        if (validationErrors.length > 0) {
            const errorMessage = `Please complete the following required fields:\n\n• ${missingFields.join('\n• ')}`;
            showToast(errorMessage, 'error');

            // Highlight missing sections visually
            highlightMissingFields(missingFields);

            // Scroll to the first missing section
            scrollToFirstMissingField(missingFields);

            return;
        }
    } else {
        // For draft, we only need a title at minimum
        if (!jobDetails.title || jobDetails.title.trim() === '') {
            showToast('Please add a job title before saving as draft', 'error');
            document.querySelector('.edit-button[onclick*="title"]').click();
            return;
        }
    }

    // Create form data to submit
    const formData = new FormData();

    // Basic job details
    formData.append('title', jobDetails.title);
    formData.append('description', description);
    formData.append('category', jobDetails.category);
    formData.append('specialty', jobDetails.specialty || '');

    // Skills (convert array to JSON string)
    formData.append('skills', JSON.stringify(jobDetails.skills || []));

    // Scope details - make sure field names match what the server expects
    if (jobDetails.scope) {
        formData.append('project_size', jobDetails.scope.size || '');
        formData.append('project_description', jobDetails.scope.description || '');

        // Handle duration properly
        if (jobDetails.scope.duration) {
            if (typeof jobDetails.scope.duration === 'object') {
                // If it's an object with value and unit
                const durationStr = `${jobDetails.scope.duration.value} ${jobDetails.scope.duration.unit}`;
                formData.append('duration', durationStr);
            } else {
                // If it's already a string
                formData.append('duration', jobDetails.scope.duration);
            }
        } else {
            formData.append('duration', '');
        }

        formData.append('experience_level', jobDetails.scope.experienceLevel || '');
    }

    // Budget details
    if (jobDetails.budget) {
        formData.append('budget_type', jobDetails.budget.type || 'fixed');
        formData.append('budget_amount', jobDetails.budget.amount || '0');

        // Add budget subtype for payment_type field
        if (jobDetails.budget.subtype) {
            formData.append('budget_subtype', jobDetails.budget.subtype);
        } else {
            // Determine subtype based on budget type for backward compatibility
            if (jobDetails.budget.type === 'hourly') {
                formData.append('budget_subtype', 'hourly');
            } else {
                formData.append('budget_subtype', 'one_time');
            }
        }

        // Add weekly time limit for hourly jobs
        if (jobDetails.budget.weeklyTimeLimit) {
            formData.append('weekly_time_limit', jobDetails.budget.weeklyTimeLimit);
        }

        // Add milestone data if it exists
        if (jobDetails.budget.milestoneDetails && jobDetails.budget.milestoneDetails.length > 0) {
            const milestoneData = {
                milestones: jobDetails.budget.milestoneDetails,
                total_amount: jobDetails.budget.amount,
                milestone_count: jobDetails.budget.milestones || jobDetails.budget.milestoneDetails.length,
                projectTitle: jobDetails.budget.projectTitle || ''  // Add project title
            };
            formData.append('milestone_data', JSON.stringify(milestoneData));
            console.log('Sending milestone data:', milestoneData);
        }
    } else {
        formData.append('budget_type', 'fixed');
        formData.append('budget_amount', '0');
        formData.append('budget_subtype', 'one_time');
    }

    // Add job_type field with a default value
    formData.append('job_type', jobDetails.job_type || 'One-time project');

    // Add status field (draft or publish)
    formData.append('status', status);

    // Debug log
    console.log('Submitting job with details:', {
        title: jobDetails.title,
        description: description,
        category: jobDetails.category,
        specialty: jobDetails.specialty,
        skills: jobDetails.skills,
        scope: jobDetails.scope,
        budget: jobDetails.budget,
        job_type: jobDetails.job_type
    });

    // Show loading state
    const submitButton = document.querySelector(status === 'draft' ? '.btn-secondary' : '.btn-primary');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + (status === 'draft' ? 'Saving Draft...' : 'Submitting...');
    submitButton.disabled = true;

    // Disable the other button too if it exists
    const otherButton = document.querySelector(status === 'draft' ? '.btn-primary' : '.btn-secondary');
    if (otherButton) {
        otherButton.disabled = true;
    }

    // Send data to server
    fetch('/submit_job', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Server response:', data);
        if (data.success) {
            // Show appropriate success message based on status
            if (status === 'draft') {
                showToast('Job saved as draft successfully!');

                // Show success animation
                submitButton.innerHTML = '<i class="fas fa-check"></i> Draft Saved!';
                submitButton.style.backgroundColor = '#4CAF50';

                // Redirect after delay
                setTimeout(() => {
                    window.location.href = '/client_page';
                }, 1500);
            } else {
                showToast('Job posted successfully!');

                // Show success animation
                submitButton.innerHTML = '<i class="fas fa-check"></i> Posted Successfully!';
                submitButton.style.backgroundColor = '#4CAF50';

                // For published jobs, show smart recommendations if experience level is filled
                if (jobDetails.scope && jobDetails.scope.experienceLevel && jobDetails.scope.experienceLevel !== '') {
                    console.log('Experience level found:', jobDetails.scope.experienceLevel);
                    console.log('Job details for recommendations:', jobDetails);

                    // Show loading message for recommendations
                    setTimeout(() => {
                        showToast('Finding matching geniuses for your job...', 'info');
                        fetchSmartRecommendations(jobDetails);
                    }, 1000);

                    // Don't automatically redirect - let user choose via buttons
                } else {
                    // No experience level, redirect normally
                    setTimeout(() => {
                        window.location.href = '/client_page';
                    }, 1500);
                }
            }
        } else {
            showToast('Error: ' + (data.error || 'An error occurred while submitting the job'), 'error');
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while submitting the job', 'error');
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM Content Loaded - Initializing page3.js");

    // Set initial job details
    jobDetails.title = document.getElementById('job-title').textContent;
    jobDetails.category = document.getElementById('job-category').textContent;
    const specialtyText = document.getElementById('job-specialty').textContent;
    jobDetails.specialty = (specialtyText === 'None' || !specialtyText.trim()) ? '' : specialtyText;

    // Check if job_type is available in the page
    const jobTypeElement = document.getElementById('job-type');
    if (jobTypeElement && jobTypeElement.textContent) {
        jobDetails.job_type = jobTypeElement.textContent;
    }

    // Check if we're in edit mode
    const isEditMode = document.querySelector('.btn-primary').textContent.includes('Update Job');

    // If in edit mode, pre-populate all fields from session data
    if (isEditMode) {
        // Try to get description from session data
        const descriptionElement = document.getElementById('job-description');
        if (descriptionElement) {
            // If the description is empty, check if there's a data attribute with the session value
            if (!descriptionElement.value && descriptionElement.dataset.sessionValue) {
                descriptionElement.value = descriptionElement.dataset.sessionValue;
                jobDetails.description = descriptionElement.dataset.sessionValue;
            }
        }

        // Try to get budget from session data
        const budgetElement = document.getElementById('job-budget');
        if (budgetElement) {
            const budgetText = budgetElement.textContent;
            if (budgetText.includes('Fixed Price')) {
                const amount = parseInt(budgetText.replace(/[^0-9]/g, '')) || 0;
                jobDetails.budget = {
                    type: 'fixed',
                    amount: amount
                };
            } else if (budgetText.includes('-')) {
                const parts = budgetText.split('-');
                const min = parseInt(parts[0].replace(/[^0-9]/g, '')) || 0;
                const max = parseInt(parts[1].replace(/[^0-9]/g, '')) || 0;
                jobDetails.budget = {
                    type: 'hourly',
                    min: min,
                    max: max
                };
            }
        }

        // Try to get skills from session data
        const skillsListElement = document.querySelector('.skills-list');
        if (skillsListElement && skillsListElement.dataset.sessionSkills) {
            try {
                // Try to parse as JSON first
                let skillsData = skillsListElement.dataset.sessionSkills;
                let skills = [];

                try {
                    skills = JSON.parse(skillsData);
                } catch (e) {
                    // If not valid JSON, try comma-separated string
                    skills = skillsData.split(',').map(s => s.trim()).filter(s => s);
                }

                if (Array.isArray(skills)) {
                    jobDetails.skills = skills;

                    // Add skills to the display
                    skills.forEach(skill => {
                        const skillTag = document.createElement('span');
                        skillTag.className = 'skill-tag';
                        skillTag.textContent = skill;
                        skillsListElement.appendChild(skillTag);
                    });
                }
            } catch (e) {
                console.error("Error parsing skills data:", e);
            }
        }

        // Initialize scope data
        const scopeSizeElement = document.getElementById('job-scope-size');
        const scopeDurationElement = document.getElementById('job-scope-duration');
        const scopeExperienceElement = document.getElementById('job-scope-experience');
        const scopeDescriptionElement = document.getElementById('job-scope-description');

        if (scopeSizeElement) {
            jobDetails.scope.size = scopeSizeElement.textContent.trim().toLowerCase();
        }

        if (scopeDurationElement) {
            const durationText = scopeDurationElement.textContent.trim();
            if (durationText && durationText !== '0') {
                const durationParts = durationText.split(' ');
                if (durationParts.length >= 2) {
                    jobDetails.scope.duration.value = parseInt(durationParts[0]) || 0;
                    jobDetails.scope.duration.unit = durationParts[1].toLowerCase();
                }
            }
        }

        if (scopeExperienceElement) {
            jobDetails.scope.experienceLevel = scopeExperienceElement.textContent.trim().toLowerCase();
        }

        if (scopeDescriptionElement) {
            jobDetails.scope.description = scopeDescriptionElement.textContent.trim();
        }

        // Update the submit button text
        const submitButton = document.querySelector('.btn-primary');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-save"></i> Update Job';
        }

        console.log("Initialized job details from session data:", jobDetails);
    }

    // Log the initial job details for debugging
    console.log("Initialized job details:", {
        title: jobDetails.title,
        category: jobDetails.category,
        specialty: jobDetails.specialty,
        job_type: jobDetails.job_type,
        isEditMode: isEditMode
    });

    // Add event listeners to category edit button
    const categoryEditButton = document.querySelector('.edit-button[onclick*="category"]');
    if (categoryEditButton) {
        console.log("Found category edit button, adding click event listener");
        categoryEditButton.addEventListener('click', function() {
            console.log("Category edit button clicked");
        });
    }

    // Add event listeners to Save Changes button in category modal
    const categorySaveButton = document.querySelector('#categoryModal .btn-primary');
    if (categorySaveButton) {
        console.log("Found category save button, adding click event listener");
        categorySaveButton.addEventListener('click', function() {
            console.log("Category save button clicked");
        });
    }

    // Add escape key listener to close modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="display: flex"]');
            openModals.forEach(modal => {
                closeModal(modal.id);
            });
        }
    });

    // Initialize job status
    updateJobStatus();
});

// Smart Recommendations System
function mapCategoryToAPI(formCategory) {
    const categoryMapping = {
        'Web, Mobile & Software Dev': 'web development',
        'Design & Creative': 'design',
        'Digital Marketing': 'digital marketing',
        'Data Science & Analytics': 'data science',
        'Writing & Content': 'writing & content',
        'Mobile Development': 'mobile development',
        'Web Development': 'web development',
        'Design': 'design',
        'Marketing': 'digital marketing',
        'Data Science': 'data science',
        'Writing': 'writing & content'
    };

    return categoryMapping[formCategory] || formCategory.toLowerCase();
}

async function fetchSmartRecommendations(jobDetails) {
    try {
        console.log('Fetching smart recommendations for job:', jobDetails);

        // Map the category to the expected API format
        const mappedCategory = mapCategoryToAPI(jobDetails.category);
        console.log('Original category:', jobDetails.category, '-> Mapped category:', mappedCategory);

        const response = await fetch('/api/smart_recommendations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                expertise: jobDetails.scope.experienceLevel,
                category: mappedCategory,
                title: jobDetails.title,
                description: jobDetails.description
            })
        });

        const data = await response.json();
        console.log('Smart recommendations response:', data);

        if (data.success) {
            console.log('Smart recommendations received:', data.recommendations.length, 'matches');
            console.log('Recommendations data:', data.recommendations);
            if (data.recommendations && data.recommendations.length > 0) {
                console.log('Showing recommendations modal...');
                showRecommendationsModal(jobDetails, data.recommendations, data.is_fallback, data.message);
            } else {
                console.log('No recommendations found');
                showToast('No matching geniuses found for your job requirements', 'warning');
            }
        } else {
            console.error('Error fetching recommendations:', data.error);
            console.error('Full error response:', data);
            showToast('Could not fetch recommendations at this time: ' + (data.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error fetching smart recommendations:', error);
        showToast('Could not fetch recommendations at this time', 'warning');
    }
}

function showRecommendationsModal(jobDetails, recommendations, isFallback = false, fallbackMessage = '') {
    // Create modal HTML
    const modalHTML = `
        <div id="smartRecommendationsModal" class="modal" style="display: block;">
            <div class="modal-content" style="max-width: 1000px; width: 95%; margin: 2% auto;">
                <div style="background: linear-gradient(135deg, #1976d2, #e91e63); color: white; padding: 25px; border-radius: 15px 15px 0 0; margin: -30px -30px 20px -30px;">
                    <h2 style="margin: 0; display: flex; align-items: center; gap: 15px;">
                        <i class="fas fa-star" style="color: #ffd700;"></i>
                        Recommended Geniuses for Your Job
                    </h2>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">Perfect matches based on your requirements</p>
                </div>

                <div style="background: ${isFallback ? '#fff3cd' : '#e8f5e8'}; padding: 15px; border-radius: 8px; border-left: 4px solid ${isFallback ? '#ffc107' : '#28a745'}; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0;">📋 Your Job: "${jobDetails.title}"</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div><strong>Category:</strong> ${jobDetails.category}</div>
                        <div><strong>Expertise:</strong> ${jobDetails.scope.experienceLevel}</div>
                        <div><strong>Found:</strong> ${recommendations.length} ${isFallback ? 'alternative' : ''} matches</div>
                    </div>
                    <div style="margin-top: 10px;">
                        <strong>${isFallback ? '⚠️ Alternative Results' : '🤖 Smart Matching'}:</strong>
                        ${isFallback ? fallbackMessage : 'Showing geniuses that match your category and expertise level'}
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; max-height: 400px; overflow-y: auto;">
                    ${recommendations.map(genius => `
                        <div style="background: white; border: 2px solid #e0e0e0; border-radius: 12px; padding: 20px; transition: all 0.3s ease; position: relative; border-top: 4px solid #1976d2;">
                            <div style="position: absolute; top: 15px; right: 15px; background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                ${genius.match_score}% Match
                            </div>
                            <div style="font-size: 18px; font-weight: bold; color: #1976d2; margin-bottom: 10px;">
                                ${genius.first_name} ${genius.last_name}
                            </div>
                            <div style="margin: 8px 0; color: #666; font-size: 14px;">
                                <strong>Position:</strong> ${genius.position || 'Not specified'}
                                <span style="background: #e3f2fd; color: #1976d2; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: bold; margin-left: 10px;">
                                    ${genius.expertise || 'N/A'}
                                </span>
                            </div>
                            <div style="margin: 8px 0; color: #666; font-size: 14px;">
                                <strong>Country:</strong> ${genius.country || 'Not specified'}
                            </div>
                            <div style="margin: 8px 0; color: #666; font-size: 14px;">
                                <strong>Rate:</strong> $${genius.hourly_rate || 'Not specified'}/hour
                            </div>
                            ${genius.match_reasons && genius.match_reasons.length > 0 ? `
                                <div style="background: #e8f5e8; padding: 10px; border-radius: 6px; margin: 10px 0; font-size: 12px; border-left: 3px solid #4caf50;">
                                    <strong style="color: #2e7d32;">Why this genius matches:</strong><br>
                                    ${genius.match_reasons.map(reason => `• ${reason}`).join('<br>')}
                                </div>
                            ` : ''}
                            ${genius.introduction ? `
                                <div style="margin: 10px 0; color: #666; font-size: 14px;">
                                    <strong>About:</strong> ${genius.introduction.substring(0, 120)}${genius.introduction.length > 120 ? '...' : ''}
                                </div>
                            ` : ''}

                            <div style="margin-top: 15px; text-align: center;">
                                <button onclick="hireGenius(${genius.id}, '${genius.first_name} ${genius.last_name}')"
                                        style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1); width: 100%;"
                                        onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.2)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                    <i class="fas fa-paper-plane"></i> Invite ${genius.first_name}
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <div style="margin-bottom: 15px;">
                        <button onclick="closeRecommendationsModal()"
                                style="background: #28a745; color: white; border: none; padding: 14px 28px; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; margin: 8px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                                onmouseover="this.style.background='#218838'; this.style.transform='translateY(-1px)'"
                                onmouseout="this.style.background='#28a745'; this.style.transform='translateY(0)'">
                            <i class="fas fa-check"></i> Great! Continue to My Jobs
                        </button>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <button onclick="cancelRecommendations()"
                                style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px; margin: 8px; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                                onmouseover="this.style.background='#5a6268'; this.style.transform='translateY(-1px)'"
                                onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                            <i class="fas fa-times"></i> Cancel - Don't Hire Now
                        </button>
                    </div>
                    <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
                        These geniuses can now see and apply to your job
                    </p>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add click outside to close
    document.getElementById('smartRecommendationsModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeRecommendationsModal();
        }
    });
}

function closeRecommendationsModal() {
    const modal = document.getElementById('smartRecommendationsModal');
    if (modal) {
        modal.remove();
    }
    // Redirect to client page
    window.location.href = '/client_page';
}

function cancelRecommendations() {
    const modal = document.getElementById('smartRecommendationsModal');
    if (modal) {
        modal.remove();
    }
    // Show message that user chose not to hire now
    showToast('Job posted successfully! You can review applications later in your dashboard.', 'info');

    // Redirect to client page after a short delay
    setTimeout(() => {
        window.location.href = '/client_page';
    }, 2000);
}

async function hireGenius(geniusId, geniusName) {
    try {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending Invitation...';
        button.disabled = true;

        console.log('Inviting genius:', geniusId, geniusName);

        // Here you can add the actual hiring logic
        // For now, let's simulate the hiring process

        // You can add API call here to create a contract or send a hiring message
        const response = await fetch('/api/hire_genius', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                genius_id: geniusId,
                genius_name: geniusName,
                action: 'hire'
            })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // Success - update button to show invitation sent state
                button.innerHTML = '<i class="fas fa-paper-plane"></i> Invitation Sent';
                button.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
                button.style.cursor = 'default';

                // Show success message
                showToast(`📩 Invitation sent to ${geniusName}! They will be notified and can accept or decline.`, 'success');

                // Don't auto-redirect, let user continue reviewing other geniuses
            } else {
                throw new Error(data.error || 'Failed to send invitation');
            }
        } else {
            throw new Error('Failed to send invitation');
        }

    } catch (error) {
        console.error('Error inviting genius:', error);

        // Reset button on error
        const button = event.target;
        button.innerHTML = originalText;
        button.disabled = false;

        // Show error message
        showToast(`Failed to send invitation to ${geniusName}. Please try again.`, 'error');
    }
}
